<?php

# EXCURSÃO E BANDA ESTÃO EM ARQUIVOS EXCLUSIVOS

// Galeria
function post_type_galeria_loja(){

  $labels = array(

    'name' => _x('Galeria Loja', 'post type general name'),
    'singular_name' => _x('Galeria Loja', 'post type singular name'),
    'add_new' => _x('Adicionar Imagem', 'galeria_loja'),
    'add_new_item' => __('Adicionar Imagem'),
    'edit_item' => __('Editar Imgans'),
    'new_item' => __('Nova Imagem'),
    'all_items' => __('Galerias - Loja'),
    'view_item' => __('Ver Imagem'),
    'search_items' => __('procurar Imagem'),
    'not_found' =>  __('Imagem não encontrada'),
    'not_found_in_trash' => __('Nenhuma imagem no Lixo'), 
    'parent_item_colon' => '',
    'menu_name' => 'Galeria'

  );

  $args = array(

    'labels' => $labels,
    'public' => true,
    'publicly_queryable' => true,
    'show_ui' => true, 
    'show_in_menu' => true, 
    'query_var' => true,
    'rewrite' => true,
    'capability_type' => 'post',
    'has_archive' => true, 
    'hierarchical' => false,
    'menu_position' => null,
    'supports' => array('title','editor','thumbnail'),
    'show_in_menu' => 'edit.php?post_type=antid_gallery'

  ); 

  register_post_type('galeria_loja',$args);

}
add_action('init', 'post_type_galeria_loja');





// Depoimentos
function create_post_type_depoimentos() {

    register_post_type( 'depoimentos',

        array(

            'labels' => array(
                'name' => __( 'Depoimentos' ),
                'singular_name' => __( 'Depoimentos' )

            ),

            'public' => true,

        )

    );

}
add_action( 'init', 'create_post_type_depoimentos' );





function create_post_type_selo() {

    register_post_type( 'selo',

        array(

            'labels' => array(
                'name' => __( 'Selo HMR' ),
                'singular_name' => __( 'Selo HMR' )

            ),

            'public' => true,

        )

    );
    
}
add_action( 'init', 'create_post_type_selo' );