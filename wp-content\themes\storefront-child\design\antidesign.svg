<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_extend "http://ns.adobe.com/Extensibility/1.0/">
	<!ENTITY ns_ai "http://ns.adobe.com/AdobeIllustrator/10.0/">
	<!ENTITY ns_graphs "http://ns.adobe.com/Graphs/1.0/">
	<!ENTITY ns_vars "http://ns.adobe.com/Variables/1.0/">
	<!ENTITY ns_imrep "http://ns.adobe.com/ImageReplacement/1.0/">
	<!ENTITY ns_sfw "http://ns.adobe.com/SaveForWeb/1.0/">
	<!ENTITY ns_custom "http://ns.adobe.com/GenericCustomNamespace/1.0/">
	<!ENTITY ns_adobe_xpath "http://ns.adobe.com/XPath/1.0/">
]>
<svg version="1.1" id="Layer_1" xmlns:x="&ns_extend;" xmlns:i="&ns_ai;" xmlns:graph="&ns_graphs;"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="80px" height="72px"
	 viewBox="0 0 80 72" enable-background="new 0 0 80 72" xml:space="preserve">
<metadata>
	<sfw  xmlns="&ns_sfw;">
		<slices></slices>
		<sliceSourceBounds  width="78.492" height="72" bottomLeftOrigin="true" y="384.5" x="258.254"></sliceSourceBounds>
	</sfw>
</metadata>
<g>
	<g opacity="0.5">
		<g>
			<g>
				<path d="M39.232,10.891h-1.807l3.357-7.018c0.03-0.07,0.092-0.07,0.12,0l3.349,7.018h-1.905l-0.265-0.588
					c0.01,0-2.596,0-2.596,0L39.232,10.891z M40.764,7.322l-0.688,1.602h1.396L40.764,7.322z"/>
				<path d="M47.273,7.453v3.438h-1.734V3.904c0-0.061,0.031-0.111,0.143-0.021l4.33,3.57V3.996h1.734v6.996
					c0,0.081-0.031,0.111-0.143,0.02L47.273,7.453z"/>
				<path d="M54.918,5.547h-1.824V3.996h5.496v1.551h-1.826v5.344h-1.846V5.547L54.918,5.547z"/>
				<path d="M59.928,3.996h1.848v6.895h-1.848V3.996L59.928,3.996z"/>
			</g>
			<g>
				<path d="M37.42,13.1h2.668c1.986,0,3.488,1.308,3.488,3.447c0,2.139-1.502,3.449-3.488,3.449H37.42V13.1z M39.885,18.434
					c1.117,0,1.807-0.741,1.807-1.887s-0.688-1.887-1.807-1.887h-0.617v3.771L39.885,18.434L39.885,18.434z"/>
				<path d="M45.547,13.1h4.836v1.551h-2.99v1.602h2.494v1.471h-2.494v0.721h3.133v1.551h-4.979V13.1L45.547,13.1L45.547,13.1z"/>
				<path d="M52.289,18.818l1.34-1.004c0.334,0.416,0.75,0.751,1.439,0.751c0.547,0,0.871-0.163,0.871-0.549
					c0-0.294-0.227-0.437-0.682-0.587l-0.812-0.274c-1.004-0.334-1.711-0.942-1.711-2.079c0-1.359,1.125-2.058,2.353-2.058
					c1.218,0,1.89,0.437,2.293,0.913l-1.057,1.105c-0.283-0.295-0.589-0.498-1.136-0.498c-0.446,0-0.67,0.202-0.67,0.478
					c0,0.264,0.149,0.396,0.485,0.507l0.912,0.305c1.379,0.455,1.847,1.115,1.847,2.088c0,1.269-0.791,2.182-2.615,2.182
					C53.598,20.096,52.715,19.387,52.289,18.818z"/>
				<path d="M59.928,13.1h1.848v6.896h-1.848V13.1L59.928,13.1z"/>
				<path d="M67.645,17.672v-1.42H70.9c0,0.041,0.012,0.153,0.012,0.295c0,2.353-1.352,3.549-3.389,3.549
					c-1.957,0-3.602-1.471-3.602-3.549c0-2.08,1.621-3.529,3.57-3.529c1.324,0,2.383,0.588,2.918,1.551l-1.473,0.893
					c-0.264-0.465-0.709-0.842-1.479-0.842c-1.094,0-1.648,0.842-1.648,1.928c0,1.084,0.588,1.947,1.723,1.947
					c0.842,0,1.229-0.305,1.439-0.822L67.645,17.672L67.645,17.672L67.645,17.672z"/>
				<path d="M74.773,16.557v3.438h-1.734v-6.986c0-0.062,0.031-0.111,0.145-0.021l4.328,3.569V13.1h1.734v6.996
					c0,0.08-0.029,0.111-0.143,0.021L74.773,16.557z"/>
			</g>
		</g>
		<g>
			<g>
				<path d="M27.119,19.996h-3.541l-5.424-3.558l-3.742,3.052l8.486-15.492h4.221V19.996L27.119,19.996z M31.24,0H20.482
					l-1.131,2.135L0.754,36l17.639-14.557l3.957,2.553h8.891L31.24,0L31.24,0z"/>
			</g>
		</g>
	</g>
	<g>
		<g>
			<g>
				<path d="M39.232,46.891h-1.807l3.357-7.018c0.03-0.072,0.092-0.072,0.12,0l3.349,7.018h-1.905l-0.265-0.59
					c0.01,0-2.596,0-2.596,0L39.232,46.891z M40.764,43.32l-0.688,1.604h1.396L40.764,43.32z"/>
				<path d="M47.273,43.453v3.438h-1.734v-6.985c0-0.062,0.031-0.11,0.143-0.021l4.33,3.57v-3.459h1.734v6.998
					c0,0.08-0.031,0.11-0.143,0.021L47.273,43.453z"/>
				<path d="M54.918,41.547h-1.824v-1.551h5.496v1.551h-1.826v5.346h-1.846V41.547L54.918,41.547z"/>
				<path d="M59.928,39.996h1.848v6.895h-1.848V39.996L59.928,39.996z"/>
			</g>
			<g>
				<path d="M37.42,49.098h2.668c1.986,0,3.488,1.312,3.488,3.447c0,2.143-1.502,3.449-3.488,3.449H37.42V49.098z M39.885,54.432
					c1.117,0,1.807-0.738,1.807-1.887c0-1.146-0.688-1.887-1.807-1.887h-0.617v3.773H39.885L39.885,54.432z"/>
				<path d="M45.547,49.098h4.836v1.553h-2.99v1.604h2.494v1.47h-2.494v0.721h3.133v1.554h-4.979V49.098L45.547,49.098
					L45.547,49.098z"/>
				<path d="M52.289,54.816l1.34-1.004c0.334,0.416,0.75,0.752,1.439,0.752c0.547,0,0.871-0.164,0.871-0.549
					c0-0.296-0.227-0.438-0.682-0.59l-0.812-0.271c-1.004-0.336-1.711-0.941-1.711-2.078c0-1.359,1.125-2.061,2.353-2.061
					c1.218,0,1.89,0.438,2.293,0.914l-1.057,1.104c-0.283-0.295-0.589-0.498-1.136-0.498c-0.446,0-0.67,0.201-0.67,0.479
					c0,0.264,0.149,0.396,0.485,0.508l0.912,0.307c1.379,0.454,1.847,1.113,1.847,2.088c0,1.268-0.791,2.182-2.615,2.182
					C53.598,56.096,52.715,55.387,52.289,54.816z"/>
				<path d="M59.928,49.098h1.848v6.896h-1.848V49.098L59.928,49.098z"/>
				<path d="M67.645,53.674v-1.42H70.9c0,0.039,0.012,0.15,0.012,0.293c0,2.354-1.352,3.549-3.389,3.549
					c-1.957,0-3.602-1.469-3.602-3.549c0-2.078,1.621-3.529,3.57-3.529c1.324,0,2.383,0.588,2.918,1.553l-1.473,0.895
					c-0.264-0.467-0.709-0.842-1.479-0.842c-1.094,0-1.648,0.842-1.648,1.926c0,1.086,0.588,1.947,1.723,1.947
					c0.842,0,1.229-0.307,1.439-0.822H67.645L67.645,53.674L67.645,53.674z"/>
				<path d="M74.773,52.557v3.439h-1.734V49.01c0-0.062,0.031-0.113,0.145-0.021l4.328,3.567V49.1h1.734v6.996
					c0,0.082-0.029,0.113-0.143,0.021L74.773,52.557z"/>
			</g>
		</g>
		<g>
			<g>
				<path d="M27.119,55.998h-3.541l-5.424-3.561l-3.742,3.054l8.486-15.493h4.221V55.998L27.119,55.998z M31.24,35.998H20.482
					l-1.131,2.135L0.754,72l17.639-14.557l3.957,2.553h8.891L31.24,35.998L31.24,35.998z"/>
			</g>
		</g>
	</g>
</g>
</svg>
