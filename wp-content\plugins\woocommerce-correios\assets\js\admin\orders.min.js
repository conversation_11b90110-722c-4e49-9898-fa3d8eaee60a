jQuery(function(a){const b={init(){a(document.body).on("click",".correios-tracking-code .dashicons-dismiss",this.removeTrackingCode).on("click",".correios-tracking-code .button-secondary",this.addTrackingCode)},block(){a("#wc-correios").block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock(){a("#wc-correios").unblock()},addTrackingFields(b){const c=a("body #wc-correios .correios-tracking-code"),d=wp.template("tracking-code-list");a(".correios-tracking-code-list",c).remove(),c.prepend(d({trackingCodes:b}))},addTrackingCode(c){c.preventDefault();const d=a("#add-tracking-code"),e=d.val();if(""===e)return;const f=b,g={action:"woocommerce_correios_add_tracking_code",security:WCCorreiosAdminOrdersParams.nonces.add,order_id:WCCorreiosAdminOrdersParams.order_id,tracking_code:e};f.block(),d.val(""),a.ajax({type:"POST",url:ajaxurl,data:g,success(a){f.addTrackingFields(a.data),f.unblock()}})},removeTrackingFields(b){const c=a("body #wc-correios .correios-tracking-code-list");1===a("li",c).length?c.remove():b.closest("li").remove()},removeTrackingCode(c){if(c.preventDefault(),!window.confirm(WCCorreiosAdminOrdersParams.i18n.removeQuestion))return;const d=b,e=a(this),f={action:"woocommerce_correios_remove_tracking_code",security:WCCorreiosAdminOrdersParams.nonces.remove,order_id:WCCorreiosAdminOrdersParams.order_id,tracking_code:e.data("code")};d.block(),a.ajax({type:"POST",url:ajaxurl,data:f,success(){d.removeTrackingFields(e),d.unblock()}})}};b.init()});