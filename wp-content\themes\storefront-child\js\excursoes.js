var current_jQ = jQuery;
	var ajaxurl = ANTIDESIGN_EXCURSOES_AJAXURL;
   current_jQ( document ).ready(function() {
       current_jQ("div.info a.reservar").on("click",function(event){
		   event.preventDefault();
           if ( typeof current_jQ(this) != "undefined" && typeof current_jQ(this).data('id') != "undefined" ){
               _id = current_jQ(this).data('id');
               var data = {
                   'action': 'reserva_excursao',
                   '_excursao': _id
               };
               // We can also pass the url value separately from ajaxurl for front end AJAX implementations
               current_jQ.post(ajaxurl, data, function(response) {
                       if ( typeof response != "undefined" && typeof response.ok != "undefined" && response.ok ){
                       
                       if ( typeof response.title != "undefined" && response.title != "" ){
current_jQ(".exc-title").html(response.title);
                           }
                           
                           current_jQ(".excursoes-form").show();
                           var deslocamento = current_jQ(".excursoes-form").offset().top;
                           var header_height = current_jQ("header#masthead").height();
                           current_jQ('html, body').animate({ scrollTop: deslocamento }, 'slow');

                       }
               });
           }
       });
       current_jQ(document).on('wpcf7:mailsent', function () {
           current_jQ(".excursoes-form").hide();
           current_jQ(".excursoes-msg").show();
           var deslocamento = current_jQ(".excursoes-msg").offset().top;

           current_jQ('html, body').animate({ scrollTop: deslocamento }, 'slow');
           setTimeout(function(){
               current_jQ(".excursoes-msg").hide();
           }, 5000);

       });
   });