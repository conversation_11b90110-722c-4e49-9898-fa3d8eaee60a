jQuery( document ).ready(function( $ ) {
 // Code using $ as usual goes here.
   _nocat = false;
   jQuery('.image_uploader').click(function(event) {
       formfield = jQuery('#wpss_upload_image').attr('name');
       tb_show('', 'media-upload.php?type=image&amp;TB_iframe=true');
       if ( jQuery( this ).hasClass("gallery_nocat") ){
           _nocat = true;
       }
       event.preventDefault();
       return false;
   });
   window.send_to_editor = function(html) {
       imgurl = jQuery('img',html).attr('src');
       arel = jQuery('img',html).parent().attr('rel');
       astring = 'wp-att-';
	   if ( typeof arel == "undefined" ){
           arel = jQuery('img',html).attr('class');
           astring = 'wp-image-';
       }
       antid_sid_ind = arel.indexOf(astring);

       antid_aid = 0;
       antid_sid_str = '';
       if ( antid_sid_ind != -1 ){
           antid_sid_ind = antid_sid_ind + astring.length;
           antid_sid_str = arel.substr( antid_sid_ind );
           antid_sid_end = antid_sid_str.indexOf(" ");

           if ( antid_sid_end != -1 ){
               antid_sid_str = antid_sid_str.substr(0, antid_sid_end);
           }
           antid_aid = antid_sid_str;
       }

       if ( _nocat ){
           if ( jQuery( "input.wpss_hide[value='"+antid_aid+"']" ).length <= 0 ){

               _input = jQuery('<input type="hidden" size="36" name="antid_fsn_btn_hid[]" class="wpss_text wpss-file wpss_hide"/>').appendTo( "#wpss-hidden_fields" );
               _input.val( antid_aid );
               _input.appendTo( "#wpss-hidden_fields" );

               if ( jQuery( "div.image_slider[data-id='"+antid_aid+"']" ).length <= 0 ){
                   _div = jQuery('<div class="image_slider" data-id=""></div>')
                   _div.attr('data-id', antid_aid );

                   _image = jQuery('<img class="wpss_img" src="" data-id="" width="500"/>');
                   _image.attr('src', imgurl );
                   _image.attr('data-id', antid_aid );
                   _image.appendTo( _div );
                   jQuery("<br/>").appendTo( _div );

                   _div.append("Título:");
                   _antid_title_pattern = "antid_titulo_";
                   _antid_title = jQuery('<input type="text" name="" value=""/>');
                   _antid_title.attr('name',( _antid_title_pattern + antid_aid ) );
                   _antid_title.appendTo( _div );
                   jQuery("<br/>").appendTo( _div );

                   _div.append("Subtítulo:");
                   _antid_sub_pattern = "antid_sub_";
                   _antid_sub = jQuery('<input type="text" name="" value=""/>');
                   _antid_sub.attr('name',( _antid_sub_pattern + antid_aid ) );
                   _antid_sub.appendTo( _div );
                   jQuery("<br/>").appendTo( _div );

                   _div.append("Link:");
                   _antid_link_pattern = "antid_link_";
                   _antid_link = jQuery('<input type="text" name="" value=""/>');
                   _antid_link.attr('name',( _antid_link_pattern + antid_aid ) );
                   _antid_link.appendTo( _div );
                   jQuery("<br/>").appendTo( _div );


                   _link = jQuery('<a href="#" data-id="" class="btn-excluir">Excluir</a>');
                   _link.attr('data-id', antid_aid );
                   _link.appendTo( _div );

                   _div.appendTo( "#wpss_upload_image_thumb" );
               }

           }
       } else {
           jQuery('#antid_fsn_btn_hid').val(antid_aid);
           jQuery('#wpss_upload_image_thumb').html("<img height='500' src='"+imgurl+"'/>");
       }




       tb_remove();


   }



jQuery('#wpss_upload_image_thumb').on('click','.btn-excluir',function(event) {
       _exclude_id = jQuery(this).data('id');
jQuery("div.image_slider[data-id='"+_exclude_id+"']").remove();
       jQuery("input.wpss_hide[value='"+_exclude_id+"']").remove();
       event.preventDefault();
   });
});