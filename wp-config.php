<?php
/**
 * The base configurations of the WordPress.
 *
 * This file has the following configurations: MySQL settings, Table Prefix,
 * Secret Keys, WordPress Language, and ABSPATH. You can find more information
 * by visiting {@link http://codex.wordpress.org/Editing_wp-config.php Editing
 * wp-config.php} Codex page. You can get the MySQL settings from your web host.
 *
 * This file is used by the wp-config.php creation script during the
 * installation. You don't have to use the web site, you can just copy this file
 * to "wp-config.php" and fill in the values.
 *
 * @package WordPress
 */
require_once dirname(__FILE__) . '/../etc/php/lib/CloudezSettings.php';
define('FS_METHOD', 'direct');
// ** MySQL settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define('DB_NAME', 'homolog_2025');
/** MySQL database username */
define('DB_USER', 'homolog2025');
/** MySQL database password */
define('DB_PASSWORD', 'FDLxBz)i17Gkl-');
/** MySQL hostname */
define('DB_HOST', 'localhost');
/** Database Charset to use in creating database tables. */
define('DB_CHARSET', 'utf8');
/** The Database Collate type. Don't change this if in doubt. */
define('DB_COLLATE', '');
define('WP_MEMORY_LIMIT', '512M');
define('WP_MAX_MEMORY_LIMIT', '1024M');

/**#@+
 * Authentication Unique Keys and Salts.
 *
 * Change these to different unique phrases!
 * You can generate these using the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}
 * You can change these at any point in time to invalidate all existing cookies. This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define('AUTH_KEY',         'N<S0UR(|2HB#TCgsp]+0UC_vNjng; $Wqq.6Ck+5|kZp@QT7H(!295Aj%!%m');
define('SECURE_AUTH_KEY',  'xMQCD[+%[3.D} h0M1Qad[N{9rjr6FeS|fZr.FwC]8G|}>z[P.B[FL:n3[Hr');
define('LOGGED_IN_KEY',    'b^[hGT++ ||K_D[SxtYGhG(ng.G>GZy1!|jU_a[ybZkzC%y[UDLNN`8X{880');
define('NONCE_KEY',        '.!ZGL_XE2Z2byf65585[(]R7y,W:bw6t},xB)6@2<nU6R`pm1y2>tcL;T)%U');
define('AUTH_SALT',        'q!tE1n.YFXLGyt`]}pU>{KWL*Q+HXfr(E/$J7T<PS/ mp3E7jHS /xNwz#GD');
define('SECURE_AUTH_SALT', 'ZQG/#H{yszUTT`4[4`mZYxqQp|N,6[ZeuZNCASmkxX/;2[Rh8:>W23ehN+er');
define('LOGGED_IN_SALT',   '<JXewE1$!#JSE@TRUc26V><wHze U>faVjFtVmQs.64Feh]97;.2e(<)3M1c');
define('NONCE_SALT',       'BP7tvsHadRYx};Et)>V`VJaZeAXp{J>|AKr0NPj[|3mhE5Nc9J<!G5%b#QM*');
define('WP_SITEURL', isset($_SERVER['HTTP_HOST']) ? (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == "on") ? 'https://'.$_SERVER['HTTP_HOST'] : 'http://'.$_SERVER['HTTP_HOST'] : 'http://homologa-hmrock.lampejos.work');
define('WP_HOME', isset($_SERVER['HTTP_HOST']) ? (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == "on") ? 'https://'.$_SERVER['HTTP_HOST'] : 'http://'.$_SERVER['HTTP_HOST'] : 'http://homologa-hmrock.lampejos.work');
/**#@-*/
/**
 * WordPress Database Table prefix.
 *
 * You can have multiple installations in one database if you give each a unique
 * prefix. Only numbers, letters, and underscores please!
 */
$table_prefix  = 'wp_';
/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 */
# define('WP_DEBUG', true);
/**
 * security
 */
define('DISALLOW_FILE_EDIT', true);
define('CONCATENATE_SCRIPTS', false);
// define( 'WP_SENTRY_PHP_DSN', 'https://<EMAIL>/4509091502358528' );
// define( 'WP_SENTRY_BROWSER_DSN', 'https://<EMAIL>/4509091502358528' );
// define('WP_SENTRY_BROWSER_ADMIN_ENABLED', true);    // Add the JavaScript tracker to the admin area. Default: true
// define('WP_SENTRY_BROWSER_LOGIN_ENABLED', true);    // Add the JavaScript tracker to the login page. Default: true
// define('WP_SENTRY_BROWSER_FRONTEND_ENABLED', true); // Add the JavaScript tracker to the front end.  Default: true
// define('WP_SENTRY_BROWSER_TRACES_SAMPLE_RATE', true); // Add the JavaScript tracker to the front end.  Default: true
/* That's all, stop editing! Happy blogging. */
/** Absolute path to the WordPress directory. */
if ( !defined('ABSPATH') )
	define('ABSPATH', dirname(__FILE__) . '/');
/** Sets up WordPress vars and included files. */
require_once(ABSPATH . 'wp-settings.php');