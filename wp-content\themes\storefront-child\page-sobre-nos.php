<?php
/**
 * The template for displaying the homepage.
 *
 * This page template will display any functions hooked into the `homepage` action.
 * By default this includes a variety of product displays and the page content itself. To change the order or toggle these components
 * use the Homepage Control plugin.
 * https://wordpress.org/plugins/homepage-control/
 *
 * Template name: Página Sobre Nós
 *
 * @package storefront
 */

get_header(); ?>
<?php $image = wp_get_attachment_image_src( get_post_thumbnail_id( $post->ID ), 'full' );?>

	<div id="primary" class="content-fullwidth">
		<main id="main" class="site-main" role="main">

			<?php while ( have_posts() ) : the_post(); ?>

				<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>


<?php if($image) { ?>
<div class="banner">
	<div class="banner-inner">
		<div class="banner-foto">
   		  	<img src="<?php echo $image[0]; ?>" class="foto"/>
   	  	</div>
	   	<div class="slider-title">
	   	<h1><span><?php the_title(); ?></span></h1>
		   	  	<?php if(get_field('titulo')) { ?>
					<h2><?php the_field('titulo'); ?></h2>
				<?php } else { ?>
				<?php } ?>
		</div>
   	  	
	</div>
</div>

<?php do_action ( 'woo_custom_breadcrumb' ); ?>

<div class="entry-content container page-banner">
<?php } else { ?>


   <header class="entry-header">
       <h1 class="entry-title"><span><?php single_post_title(); ?></span></h1>
       <?php if(get_field('titulo')) { ?>
					<h2><?php the_field('titulo'); ?></h2>
				<?php } else { ?>
				<?php } ?>
   </header><!-- .page-header -->
   <?php do_action ( 'woo_custom_breadcrumb' ); ?>
   <div class="entry-content container">

<?php } ?>

<div class="column-content">
<?php the_content(); ?><div class="depoimentos-border">
	<div class="galeria">
		<?php $loop = new WP_Query( array( 'post_type' => 'galeria_loja', 'posts_per_page' => -1 ) ); ?>
		<?php while ( $loop->have_posts() ) : $loop->the_post(); ?>
		<?php $image_gal = wp_get_attachment_image_src( get_post_thumbnail_id( $post->ID ), 'large' );?>
			<li>
				<div class="info">
					<img src="<?php echo $image_gal[0]; ?>" class="foto"/>
				</div>
			</li>
		<?php endwhile; wp_reset_query(); ?>
	</div>
</div>
</div>
<div class="column-broken">
<div class="verticalline">
		<h2>O QUE DIZEM NOSSOS CLIENTES</h2>
</div>
</div>
							
<div class="column-lista">
	<div class="depoimentos-border">
		<div class="banner depoimentos">
		<?php $loop = new WP_Query( array( 'post_type' => 'depoimentos', 'posts_per_page' => -1 ) ); ?>
		<?php while ( $loop->have_posts() ) : $loop->the_post(); ?>
			<li>
				<div class="info">
					<h3><?php the_title(); ?></h3>
					<?php the_content(); ?>
				</div>
			</li>
		<?php endwhile; wp_reset_query(); ?>
		</div>
	</div>
</div>	

</div>


</article><!-- #post-## -->

			<?php endwhile; // end of the loop. ?>

		</main><!-- #main -->
	</div><!-- #primary -->

<?php get_footer(); ?>
