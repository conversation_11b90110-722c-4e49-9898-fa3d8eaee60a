<?php
/**
 * Loop Price
 *
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     1.6.4
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global $product;
?>

<?php if ( $price == $product->get_sale_price() ) : ?>
		<span class="price"><?php echo $product->get_price(); ?></span>
<?php else: ?>
		<span class="price-promo"><?php echo $product->get_price(); ?></span>
		<span class="price-old"><?php echo $product->get_regular_price(); ?></span>
<?php endif; ?>
