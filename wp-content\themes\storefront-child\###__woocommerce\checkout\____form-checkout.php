<?php
/**
 * Checkout Form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-checkout.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce/Templates
 * @version 3.5.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

do_action( 'woocommerce_before_checkout_form', $checkout );

// If checkout registration is disabled and not logged in, the user cannot checkout.
if ( ! $checkout->is_registration_enabled() && $checkout->is_registration_required() && ! is_user_logged_in() ) {
	echo esc_html( apply_filters( 'woocommerce_checkout_must_be_logged_in_message', __( 'You must be logged in to checkout.', 'woocommerce' ) ) );
	return;
}

?>

<style type="text/css">
    #tabs .hide {
        display:none;
    }
    h3#ship-to-different-address {
    
    }
    .woocommerce-billing-fields h3 {
        display:none;
    }
    .steps-set {
        height:auto;
    }
    .woocommerce-shipping-fields .shipping_address {
        
    }
    div#box-checkout .woocommerce-info.antidesign-checkout {
        display:none;
    }
    div#box-checkout .woocommerce-info.antidesign-checkout.on {
        display:block;
    }
    .woocommerce-Address .container-address {
        display:none;
    }
    .tab_payment .woocommerce-Address .container-address {
        display:block;
    }
    .tab_payment .woocommerce-Address .woocommerce-address-billing,.tab_payment .woocommerce-Address .woocommerce-address-shipping {
        display:none;
    }
    .address-payment-title {
        display:none;
    }
    .tab_payment .address-payment-title {
        display:block;
    }
    .tab_payment .address-title {
        display:none;
    }


    /* custom logos  */
    #payment .payment_methods li img {
        max-height: 1.9em;
    }
    #payment .payment_methods li.payment_method_bacs label {
        text-indent: -9000em;
        background: url('<?php echo get_template_directory_uri(); ?>-child/design/gateways-logos/method_bacs.png') left top no-repeat;
        background-size: contain;
        height: 28px;
    }

    #payment .payment_methods li.payment_method_pagarme-banking-ticket label {
        text-indent: -9000em;
        background: url('<?php echo get_template_directory_uri(); ?>-child/design/boleto.jpg') left top no-repeat;
        height: 28px;
    }

 
    #payment .payment_methods li.payment_method_wc-ppp-brasil-gateway .payment_method_wc-ppp-brasil-gateway {
        max-height: -webkit-fill-available;
        margin-bottom: 0;
       /* max-height: 500px;*/
    }

/*
    #payment .payment_methods li.payment_method_wc-ppp-brasil-gateway .payment_method_wc-ppp-brasil-gateway #wc-ppb-brasil-wrappers #wc-ppp-brasil-container,
     #payment .payment_methods li.payment_method_wc-ppp-brasil-gateway .payment_method_wc-ppp-brasil-gateway #wc-ppb-brasil-wrappers #wc-ppp-brasil-container iframe {
        height: 500px;
    }
*/

    @media screen and (max-width: 1200px) {
        #payment .payment_methods li label {
            width: 90%;
        }
    }
        

</style>

<div id="box-checkout">
    <div class="woocommerce-info antidesign-checkout">
        
    </div>
    <div class="steps-set" id="tabs">
        <div class="steps-mark">
            <ul class="progressbar">
                <li class="active"><a href="#tab-1">Endereço</a></li>
                <li><a href="#tab-2">Entrega</a></li>
                <li><a href="#tab-3">Pagamento</a></li>
            </ul>
        </div>
        <div class="woocommerce-checkout-coupon" style="display:none;">
            <?php do_action( 'antid_woocommerce_coupon_form' ); ?>
        </div>

<form name="checkout" method="post" class="checkout woocommerce-checkout" action="<?php echo esc_url( wc_get_checkout_url() ); ?>" enctype="multipart/form-data">

	<?php if ( $checkout->get_checkout_fields() ) : ?>

		<?php do_action( 'woocommerce_checkout_before_customer_details' ); ?>

		<div class="col2-set addresses" id="customer_details">
			<div class="col-1">
				<?php do_action( 'woocommerce_checkout_billing' ); ?>
                            <a class="button btn-address-next">Próximo</a>
			</div>

			<div class="col-2 woocommerce-Address">
				<?php do_action( 'woocommerce_checkout_shipping' ); ?>
                            <a class="button btn-address-next">Próximo</a>
			</div>
		</div>

		<?php do_action( 'woocommerce_checkout_after_customer_details' ); ?>

	<?php endif; ?>
    
        <?php do_action( 'woocommerce_checkout_before_order_review' ); ?>
	<div id="order_review" class="woocommerce-checkout-review-order">
		<?php do_action( 'woocommerce_checkout_order_review' ); ?>
	</div>
        <?php do_action( 'woocommerce_checkout_after_order_review' ); ?>
    <?php
    /*
    
    
	<h3 id="order_review_heading"><?php esc_html_e( 'Your order', 'woocommerce' ); ?></h3>

	<?php do_action( 'woocommerce_checkout_before_order_review' ); ?>

	<div id="order_review" class="woocommerce-checkout-review-order">
		<?php do_action( 'woocommerce_checkout_order_review' ); ?>
	</div>

	<?php do_action( 'woocommerce_checkout_after_order_review' ); ?>
     * 
     */
    ?>

</form>
    </div>
</div>

<?php do_action( 'woocommerce_after_checkout_form', $checkout ); ?>

<script
  src="https://code.jquery.com/jquery-3.5.1.slim.min.js"
  integrity="sha256-4+XzXVhsDmqanXGHaHvgh1gMQKX40OUvDEBTu8JcmNs="
  crossorigin="anonymous"></script>

<style type="text/css">
    #page{
        overflow: hidden;
    }
    .column-step{
        box-shadow: 0 0 0 0;
        border: 0 none;
        outline: 0;
    }
</style>

<script type="text/javascript">

    $('.progressbar a').click(function () {
        var return_result = verificar_campos();
        if(return_result == true){
            var visible = $(this).attr('href').slice(1);
            $('.column-step').css('display', 'none');
            $('#'+visible).css('display', 'block');
            $('.progressbar li').removeClass('active');
            if(visible == 'tab-1'){
                var father = $("a[href$='#tab-1']").parent();
                $(father).addClass('active');
            }else if(visible == 'tab-2'){
                var father = $("a[href$='#tab-1']").parent();
                $(father).addClass('active');
                var father = $("a[href$='#tab-2']").parent();
                $(father).addClass('active');
            }else if(visible == 'tab-3'){
                var father = $("a[href$='#tab-1']").parent();
                $(father).addClass('active');
                var father = $("a[href$='#tab-2']").parent();
                $(father).addClass('active');
                var father = $("a[href$='#tab-3']").parent();
                $(father).addClass('active');
            }
        }
    });

    $('#tab-1').on('click', '.button', function () {
        var return_result = verificar_campos();
        if(return_result == true){
            $('#tab-1').css('display', 'none');
            $('#tab-2').css('display', 'block');
            var father = $("a[href$='#tab-1']").parent();
            $(father).addClass('active');
            var father = $("a[href$='#tab-2']").parent();
            $(father).addClass('active');
        }
    });

    $('#tab-2').on('click', '.button', function () {
        var return_result = verificar_campos();
        if(return_result == true){
            $('#tab-2').css('display', 'none');
            $('#tab-3').css('display', 'block');
            var father = $("a[href$='#tab-1']").parent();
            $(father).addClass('active');
            var father = $("a[href$='#tab-2']").parent();
            $(father).addClass('active');
            var father = $("a[href$='#tab-3']").parent();
            $(father).addClass('active');
        }
    });

    function verificar_campos(){
        var nome                                    = $('#billing_first_name').val();
        var sobre_nome                      = $('#billing_last_name').val();
        var cpf                                         = $('#billing_cpf').val();
        var nascimento                      = $('#billing_birthdate').val();
        var cep                                         = $('#billing_postcode').val();
        var endereco                            = $('#billing_address_1').val();
        var billing_number              = $('#billing_number').val();
        var bairro                                  = $('#billing_neighborhood').val();
        var cidade                              = $('#billing_city').val();
        var estado                              = $('#billing_state').val();
        var telefone                                = $('#billing_phone').val();
        var email                                   = $('#billing_email').val();
        if(!nome || !sobre_nome || !cpf || !nascimento || !cep || !endereco || !billing_number || !bairro || !cidade || !estado || !telefone || !email){
            alert('Ops! Existem campos obrigatórios que não foram preenchidos.')
            return false;
        }else{
            setTimeout(function(){ window.scrollTo(0, 0); }, 30);
            return true;
        }
    }

</script>