<?php
/**
 * The template for displaying the homepage.
 *
 * This page template will display any functions hooked into the `homepage` action.
 * By default this includes a variety of product displays and the page content itself. To change the order or toggle these components
 * use the Homepage Control plugin.
 * https://wordpress.org/plugins/homepage-control/
 *
 * Template name: Página LPs Usados
 *
 * @package storefront
 */
remove_action( "wp_footer", "antidesign_fix_nc_wishlist", 9 );

get_header(); ?>
  <?php do_action ( 'woo_custom_breadcrumb' ); ?>
	<div id="primary" class="content-area">
		<main id="main" class="site-main" role="main">
			
			<?php while ( have_posts() ) : the_post(); ?>
				
				<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>

<h1 class="page-title">LPs Usados</h1><br>
   <div class="entry-content container">
				<?php the_content(); ?>
		</div>
				</article><!-- #post-## -->

			<?php endwhile; // end of the loop. ?>
<style type="text/css">
    .scroll {
        position:absolute;
        width:100%;
        height:0;
    }
    .scrollToTop{
                position:fixed;
                width:50px; 
                height:50px;
                padding:15px 0; 
                text-align:center; 
                background: #000;
                font-weight: bold;
                color: #FFF;
                text-decoration: none;
                bottom:75px;
                right:40px;
                display:none;
                margin:0;
        }
    @media screen and (min-width: 768px) {
        .scrollToTop{
                position:absolute;
        }
    }

    .scrollToTop.fixed {
            position:fixed;
            width:50px; 
            height:50px;
            padding:15px 0; 
            text-align:center; 
            background: #000;
            font-weight: bold;
            color: #FFF;
            text-decoration: none;
            bottom:75px;
            right:40px;
            display:none;
    }

    .scrollToTop:hover{
            text-decoration:none;
    }
}
</style>
<script src="<?php echo get_stylesheet_directory_uri(); ?>/js/jquery-2.1.4.min.js"></script>
<script type="text/javascript">

jQuery(document).ready(function(){
	
	//Check to see if the window is top if not then display button
	jQuery(window).scroll(function(){
		var offset = 0;
		var article_height = 0;
                var __min_width = 768;
                
                _bottom = 75;
		if ( typeof jQuery("article").offset() != "undefined" ){
			offset = jQuery("article").offset().top;
		}
		
		var minTop = 0;
		if ( offset > 0 ){
			minTop = minTop + offset;
		} else {
			minTop = 100;
		}
		
		
		var maxTop = article_height + minTop;
		
		if ( jQuery(this).scrollTop() > minTop ) {
			jQuery('.scrollToTop').fadeIn();
		} else {
			jQuery('.scrollToTop').fadeOut();
		}
                var __limit = jQuery('article').offset().top + jQuery('article').height();
                var _yyy = jQuery(this).scrollTop() + jQuery(window).height();
                __footer_height = jQuery("footer").prop('offsetHeight');
                
                
                
                if ( _yyy > __limit ) {
                    jQuery('.scrollToTop').removeClass("fixed");
                    _bottom = _bottom + ( _bottom * 2 );
                    _bottom = _bottom + __footer_height;
                } else {
                    jQuery('.scrollToTop').removeClass("fixed").addClass('fixed');
                }
                if ( jQuery(window).width() > __min_width ){
                    jQuery('.scrollToTop').css( "bottom", _bottom );
                }
                
	});
	
	//Click event to scroll to top
	jQuery('.scrollToTop').click(function(){
		var target = 0;
		var offset = 0;
		if ( typeof jQuery("div.fixedmenu").height() != "undefined" ){
			offset = jQuery("div.fixedmenu").height();
		}
		
		if ( typeof jQuery("article").offset() != "undefined" ){
			target = jQuery("article").offset().top;
			if ( offset > 0 ){
				target = target - offset;
			}
		}
		jQuery('html, body').animate({scrollTop : target },800);
		return false;
	});
	
});

</script>
		</main><!-- #main -->
	</div><!-- #primary -->
<div id="secondary" class="widget-area" role="complementary"> 
    <aside id="text-5" class="widget widget_text">
    		<h3 class="widget-title">Categorias</h3>
    		<div class="textwidget">
	    		<a href="http://hmrock.com.br/categoria-produto/lps/novos/" class="botao easy">Novos</a>
				<a href="http://hmrock.com.br/lps-usados/" class="botao easy">Usados</a>
			</div>
		</aside> 
    </div>
<?php $antid_show_scroll = true; ?>
<?php get_footer(); ?>