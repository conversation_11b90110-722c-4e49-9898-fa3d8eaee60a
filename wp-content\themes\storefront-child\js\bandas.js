   jQuery( document ).ready(function() {
       jQuery("div.abcd a[data-id]").on("click",function(event){
		   event.preventDefault();
           if ( typeof jQuery(this) != "undefined" && typeof jQuery(this).data('id') != "undefined" ){
				var _id = jQuery(this).data('id'),
					offset = 0,
					offset_el;
				
				el = jQuery(".letra-box#" + _id );
				if( typeof el != "undefined" && el != null && typeof el.offset === "function" && el.offset() != null ){
					offset_el = el.offset();
					if( typeof offset_el != "undefined" && offset_el != null && typeof offset_el.top != "undefined" && offset_el.top != null && !isNaN( parseFloat ( offset_el.top ) ) ){
						offset += parseFloat( offset_el.top );
					}
				}
				if( offset > 0 ){
					height = jQuery( ".fixedmenu" ).height();
					if( typeof height != "undefined" && height != null && !isNaN( parseInt( height ) ) ){
						offset -= parseInt( height );
					}
					jQuery('html, body').animate({
						scrollTop : offset
					}, 1000 );
				}
           }
       });
       
   });