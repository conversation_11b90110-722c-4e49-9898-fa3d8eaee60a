<?php
/**
 * The template for displaying full width pages.
 *
 * Template Name: User
 *
 * @package storefront
 */

get_header(); ?>
<?php if ( is_user_logged_in() ) : ?>
<div id="primary" class="content-area">
		<main id="main" class="site-main" role="main">

			<?php while ( have_posts() ) : the_post(); ?>

				<?php
				do_action( 'storefront_page_before' );
				?>

				<?php get_template_part( 'content', 'page' ); ?>

				<?php
				/**
				 * @hooked storefront_display_comments - 10
				 */
				do_action( 'storefront_page_after' );
				?>

			<?php endwhile; // end of the loop. ?>

		</main><!-- #main -->
	</div><!-- #primary -->
	<div id="secondary" class="widget-area minha-conta">
	<aside id="nav_menu-2" class="widget widget submenu">


	<h3 class="widget-title">Painel</h3>
	<a href="<?php echo wc_get_account_endpoint_url( 'orders' ); ?>" class="botao easy"><span class="icon-bookmark2"></span>Meus pedidos</a>
	<a href="<?php echo home_url(); ?>/minha-conta/meus-pontos/" class="botao easy"><span class="icon-trophy"></span>Meus Pontos</a>
	<a href="<?php echo home_url(); ?>/programa-de-pontos-hmrock/" class="botao easy"><span class="icon-info"></span>Programa de Pontos</a>
	<a href="<?php echo wc_get_account_endpoint_url( 'edit-account' ); ?>" class="botao easy"><span class="icon-user2"></span>Meus dados</a>
	<a href="<?php echo wc_get_account_endpoint_url( 'edit-address/cobranca' ); ?>" class="botao easy"><span class="icon-home4"></span>Endereço de Cobrança</a>
	<!--<a href="<?php echo wc_get_account_endpoint_url( 'edit-address/entrega' ); ?>" class="botao easy">Endereço de Entrega </a>-->
	<!-- <a href="<?php echo home_url(); ?>/lista-de-desejos/" class="botao easy"><span class="icon-heart2"></span>Lista de Desejos</a> -->
	<!-- <a href="<?php echo home_url(); ?>/lista-de-espera/" class="botao easy"><span class="icon-clock3"></span>Lista de Espera</a> -->
	<a href="<?php echo wc_get_account_endpoint_url( 'customer-logout' ); ?>" class="botao easy"><span class="icon-cross"></span>Sair</a>
	<!--<?php if ( get_post_meta($post->ID, 'CustomSideBar', true) ) : ?>
		<h1 class="widget-title">Informações</h1>
		<? echo apply_filters('the_content', get_post_meta($post->ID, 'CustomSideBar', true)); ?>
	<?php endif; ?>-->
	</aside>
	</div>

<?php else : ?>
<div class="page-template-template-fullwidth page-template-template-fullwidth-php">
	<div id="primary" class="content-fullwidth">
		<main id="main" class="site-main" role="main">

			<?php while ( have_posts() ) : the_post(); ?>

				<?php
				do_action( 'storefront_page_before' );
				?>

				<?php get_template_part( 'content', 'page' ); ?>

				<?php
				/**
				 * @hooked storefront_display_comments - 10
				 */
				do_action( 'storefront_page_after' );
				?>

			<?php endwhile; // end of the loop. ?>

		</main><!-- #main -->
	</div><!-- #primary -->
</div>
<?php endif; ?>
<?php get_footer(); ?>