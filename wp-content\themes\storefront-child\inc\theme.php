<?php 

function theme_widgets_init() {

  register_sidebar( array (

    'name' => 'Banner em todas as páginas',
    'id' => 'banner_widget_area',
    'before_widget' => '',
        'after_widget' => '',
        'before_title' => '<h2>',
        'after_title' => '</h2>',
    ) );

}
add_action( 'init', 'theme_widgets_init' );


/*add_action( 'init', 'storefront_custom_logo' );
function storefront_custom_logo() {
	remove_action( 'storefront_header', 'storefront_site_branding', 20 );
	add_action( 'storefront_header', 'storefront_display_custom_logo', 20 );
}



function storefront_display_custom_logo() {
$img = get_stylesheet_directory_uri().'/design/hmr-logo.png';
?>
	<a href="<?php echo esc_url( home_url( '/' ) );
	 ?>" class="site-logo-link" rel="home">
		<img src="<?php echo $img;
		?>" alt="<?php echo get_bloginfo( 'name' );
		 ?>" />
	</a>
<?php
}

*/

# FOOTER - Seção de pagamentos - Bandeiras e logos

function storefront_credit() {
?>
    <section class="footer-widgets col-4 fix footer-pagamentos">
        <h3>PAGAMENTOS</h3>
        <center>
        <img src="<?php echo get_stylesheet_directory_uri(); ?>/design/banco-logos/banner-pagseguro.gif" border="0" width="560" height="50" alt="PagSeguro" />
        <img src="<?php echo get_stylesheet_directory_uri(); ?>/design/banco-logos/banner-paypal.png" border="0" width="505" height="50" alt="Paypall"/>
        <img src="<?php echo get_stylesheet_directory_uri(); ?>/design/banco-logos/banner-caixa.png" border="0" width="114" height="50" alt="Caixa"/>
        <?php /*
        <a id="seloEbit" href="http://www.ebit.com.br/#heavy-metal-rock" target="_blank" value="1">Avaliação de Lojas e-bit</a>
        <script type="text/javascript" id="getSelo" src="https://imgs.ebit.com.br/ebitBR/selo-ebit/js/getSelo.js?5828" >
        </script>
        */ ?>
        </center>

    </section>
    
    
    <div class="site-info">
        <div class="columns col-2">&copy; <?php echo date('Y') . ' - ' . get_bloginfo( 'name' ) . ' | CNPJ: 52.941.556/0001-86'; ?></div>
        <div class="columns col-2 assinaturas"><a href="http://www.antidesign.com.br" id="antidesign" target="_blank">AntiDesign.com.br</a></div>
    </div><!-- .site-info -->
<?php
}



// LOGO NA PAGINA DE LOGIN
function my_login_logo() {
 ?>
    <style type="text/css">
        body.login div#login h1 a {

        display: block;

	      width: 80px;

	      height: 160px;

	      background: url(<?php echo get_stylesheet_directory_uri(); ?>/design/hmr-logo.png) no-repeat;

	      background-size: 100%;

	      font: 0px/0 a;

	      text-shadow: none;

	      color: transparent;

	      position: relative!important;

	      margin-bottom: 40px;

	      z-index: 4;

        }

    </style>
<?php }

add_action( 'login_enqueue_scripts', 'my_login_logo' );



function my_login_logo_url() {

    return get_bloginfo( 'url' );

}

add_filter( 'login_headerurl', 'my_login_logo_url' );



function my_login_logo_url_title() {

    return 'Heavy Metal Hock';

}

add_filter( 'login_headertitle', 'my_login_logo_url_title' );



$url_template = get_template_directory().'/antidesign';

if ( file_exists( get_template_directory().'-child/antidesign/' ) ){

   $url_template = get_template_directory().'-child/antidesign';

}



/*function antid_banner_home () {

   $settings = get_option( "ilc_theme_settings" );

    $html .= "";

    if ( is_array( $settings ) && isset( $settings['antid_gallery'] ) && is_array( $settings['antid_gallery'] ) ){

        $antid_gallery = $settings['antid_gallery'];

        foreach( $antid_gallery as $img_id => $info ){

            if ( gettype( $img_id ) == "integer" ){

                $image = wp_get_attachment_image_src ( $img_id, 'full' );

                $image_src = "";

                if ( is_array( $image ) && isset( $image[0] ) ){

                    $image_src = $image[0];

                    $image_attr = "";

                    if ( isset( $image[1] ) && $image[1] > 1280 ){

                        //$image_attr = ' width="1280"';

                    }

                    $html_titulo = "";

                    $html_subtitulo = "";

                    $start_link = "";

                    $end_link = "";

                    if ( is_array( $info ) && isset( $info["titulo"] ) ){

                        $html_titulo = "<h1><span>" . $info["titulo"] . "</span></h1>";

                    }

                    if ( is_array( $info ) && isset( $info["sub"] ) ){

                        $html_subtitulo = "<h2>" . $info["sub"] . "</h2>";

                    }

                    if ( is_array( $info ) && isset( $info["link"] ) && $info["link"] != "" ){

                           $start_link = '<a href="'.$info["link"].'">';

                           $end_link   = '</a>';

                       }

                    $div_title = "";

                    if ( $html_titulo != "" || $html_subtitulo != "" ){

                        $div_title = '<div class="slider-title">'.$html_titulo . $html_subtitulo . '</div>';

                    }

                    $html .= '  <div>
                                   '.$start_link.'
                                   <img src="'.$image_src.'"'.$image_attr.'/>
                                   '.$div_title.'
                                   '.$end_link.'
                               </div>';

                }

            }

        }

    }

    if ( $html != "" ){

        echo '<div class="banner slick home">' . $html . '</div>';

    }

}

add_action( 'antid_banner_home', 'antid_banner_home', 5);
 */


function storefront_before_content() {

    $is_product = false;

    if ( is_single() ){

        $current_product = get_queried_object();

        if ( is_object( $current_product ) && isset( $current_product->post_type ) && $current_product->post_type == "product" ){

            $is_product = true;

        }

    }

    if ( $is_product ){

?> 
        <div id="primary" class="content-fullwidth"> 
        <main id="main" class="site-main" role="main"> 
<?php 
    }
     else {

?> 
        <div id="primary" class="content-area"> 
        <main id="main" class="site-main" role="main"> 
<?php 
    }

}



function antid_dados_bancarios($fields){

   $fields_old = $fields;


   $fields = array();


   $bacs = get_option( 'woocommerce_bacs_accounts' );


   $bank_name = "";


   if ( is_array( $bacs ) && isset( $bacs[0] ) ){

       $bacs = $bacs[0];

   }

   $bacs = (object) $bacs;




   $banco_url = "";

   $banco_img =  '/design/banco-logos/caixa.png';

    if ( file_exists( get_stylesheet_directory() . $banco_img ) ){

        $banco_url = get_stylesheet_directory_uri() . $banco_img;

    }



    $width = "70px";

    $padding = "padding:10px;
    ";

    $img_banco = "";

    if ( $banco_url != "" ){

        $img_banco = '<img src="'.$banco_url.'" style="width:'.$width.';
        '.$padding.'">';

    }

    $fields['bank_image'] = array(
        'label' => '',
        'value' => $img_banco
    );


    if( isset( $bacs->bank_name ) && $bacs->bank_name != "" ){

        $fields['bank_name'] = array(
            'label' => 'Banco',
            'value' => $bacs->bank_name
        );

    }

   
    if( isset( $bacs->account_name ) && $bacs->account_name != "" ){

        $fields['account_name'] = array(
            'label' => __( 'Account name', 'woocommerce' ),
            'value' => $bacs->account_name
        );

    }


    if( isset( $bacs->iban ) && $bacs->iban != "" ){

        $fields['cpf'] = array(
            'label' => __( 'CPF', 'woocommerce' ),
            'value' => $bacs->iban
        );

    }


    if( isset( $bacs->sort_code ) && $bacs->sort_code != "" ){

        $fields['sort_code'] = array(
            'label' => __( 'Agência', 'woocommerce' ),
            'value' => $bacs->sort_code
        );

    }



    if( isset( $bacs->account_number ) && $bacs->account_number != "" ){

        $fields['account_number'] = array(
            'label' => __( 'Account number', 'woocommerce' ),
            'value' => $bacs->account_number
        );

    }

   
    if( isset( $bacs->iban ) && $bacs->bic != "" ){

        $fields['email_comrovante'] = array(
            'label' => __( 'Enviar comprovante para', 'woocommerce' ),
            'value' => $bacs->bic
        );

    }


    return $fields;

}

add_filter('woocommerce_bacs_account_fields','antid_dados_bancarios');



//ALTERAÇÕES
add_filter( "woocommerce_checkout_must_be_logged_in_message", function( $html = "" ){

    if( is_checkout() ){

        return "";

    }

    return $html;

}
 );


add_filter( "antidesign_break_checkout", function( $break = false, $display_login = false ){

    if( !$break ){

        if( $display_login ){

            return true;

        }

    }

    return $break;

}
, 9999, 2 );


add_action( "wp_enqueue_scripts", function(){

       wp_enqueue_script( 'antd_fix-wc-cep', get_stylesheet_directory_uri() .'/js/fix-wc-cep.js', array( 'jquery', 'wc-country-select', /*'wc-address-i18n' */ ) );

}
, 1 );


/* REMOVER VERSÃO DO WP */
/*function wpbeginner_remove_version() {

    return '';

}

add_filter('the_generator', 'wpbeginner_remove_version');
*/


/* ERROS PERSONALIZADOS E NÃO GENÉRICOS */
/*function guwp_error_msgs($msg='') {

    global $action;

    if ( $action == "login" ){

        $custom_error_msgs = array(
            '<strong>Atenção</strong> Você não tem permissão para entrar!',
            '<strong>Bloqueio!</strong> Se errar a senha mais uma vez seu IP será bloqueado!',
        );

        return $custom_error_msgs[array_rand($custom_error_msgs)];

    }
     else {

        return $msg;

    }

}

add_filter( 'login_errors', 'guwp_error_msgs' );
*/

