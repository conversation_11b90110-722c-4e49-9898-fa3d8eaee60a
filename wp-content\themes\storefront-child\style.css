/*
Theme Name: Storefront - Child
Theme URI: http://www.antidesign.com.br
Author: AntiDesign
Author URI: http://www.antidesign.com.br
Template: storefront
Version: 1.1

*/

@import url("../storefront/style.css");

@font-face {
    font-family: 'icometal';
    src: url('icometal/fonts/icometal.eot');
    src: url('icometal/fonts/icometal.eot#iefix') format('embedded-opentype'), url('icometal/fonts/icometal.ttf') format('truetype'), url('icometal/fonts/icometal.woff') format('woff'), url('icometal/fonts/icometal.svg#icometal') format('svg');
    font-weight: normal;
    font-style: normal;
}

[class^="icon-"],
[class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icometal' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-box:before {
    content: "\e900";
}

.icon-write:before {
    content: "\e901";
}

.icon-clock3:before {
    content: "\e902";
}

.icon-reply2:before {
    content: "\e903";
}

.icon-reply-all:before {
    content: "\e904";
}

.icon-forward4:before {
    content: "\e905";
}

.icon-flag2:before {
    content: "\e906";
}

.icon-search2:before {
    content: "\e907";
}

.icon-trash:before {
    content: "\e908";
}

.icon-envelope:before {
    content: "\e909";
}

.icon-bubble3:before {
    content: "\e90a";
}

.icon-bubbles5:before {
    content: "\e90b";
}

.icon-user2:before {
    content: "\e90c";
}

.icon-users2:before {
    content: "\e90d";
}

.icon-cloud2:before {
    content: "\e90e";
}

.icon-download4:before {
    content: "\e90f";
}

.icon-upload4:before {
    content: "\e910";
}

.icon-rain:before {
    content: "\e911";
}

.icon-sun2:before {
    content: "\e912";
}

.icon-moon:before {
    content: "\e913";
}

.icon-bell2:before {
    content: "\e914";
}

.icon-folder2:before {
    content: "\e915";
}

.icon-pin:before {
    content: "\e916";
}

.icon-sound:before {
    content: "\e917";
}

.icon-microphone:before {
    content: "\e918";
}

.icon-camera2:before {
    content: "\e919";
}

.icon-image2:before {
    content: "\e91a";
}

.icon-cog2:before {
    content: "\e91b";
}

.icon-calendar2:before {
    content: "\e91c";
}

.icon-book2:before {
    content: "\e91d";
}

.icon-map-marker:before {
    content: "\e91e";
}

.icon-store:before {
    content: "\e91f";
}

.icon-support:before {
    content: "\e920";
}

.icon-tag:before {
    content: "\e921";
}

.icon-heart2:before {
    content: "\e922";
}

.icon-video-camera2:before {
    content: "\e923";
}

.icon-trophy2:before {
    content: "\e924";
}

.icon-cart2:before {
    content: "\e925";
}

.icon-eye2:before {
    content: "\e926";
}

.icon-cancel:before {
    content: "\e927";
}

.icon-chart:before {
    content: "\e928";
}

.icon-target2:before {
    content: "\e929";
}

.icon-printer2:before {
    content: "\e92a";
}

.icon-location3:before {
    content: "\e92b";
}

.icon-bookmark2:before {
    content: "\e92c";
}

.icon-monitor:before {
    content: "\e92d";
}

.icon-cross2:before {
    content: "\e92e";
}

.icon-plus2:before {
    content: "\e92f";
}

.icon-left:before {
    content: "\e930";
}

.icon-up:before {
    content: "\e931";
}

.icon-browser:before {
    content: "\e932";
}

.icon-windows2:before {
    content: "\e933";
}

.icon-switch2:before {
    content: "\e934";
}

.icon-dashboard:before {
    content: "\e935";
}

.icon-play4:before {
    content: "\e936";
}

.icon-fast-forward:before {
    content: "\e937";
}

.icon-next3:before {
    content: "\e938";
}

.icon-refresh:before {
    content: "\e939";
}

.icon-film2:before {
    content: "\e93a";
}

.icon-home4:before {
    content: "\e93b";
}

.icon-home:before {
    content: "\e93c";
}

.icon-home2:before {
    content: "\e93d";
}

.icon-home3:before {
    content: "\e93e";
}

.icon-office:before {
    content: "\e93f";
}

.icon-newspaper:before {
    content: "\e940";
}

.icon-pencil:before {
    content: "\e941";
}

.icon-pencil2:before {
    content: "\e942";
}

.icon-quill:before {
    content: "\e943";
}

.icon-pen:before {
    content: "\e944";
}

.icon-blog:before {
    content: "\e945";
}

.icon-eyedropper:before {
    content: "\e946";
}

.icon-droplet:before {
    content: "\e947";
}

.icon-paint-format:before {
    content: "\e948";
}

.icon-image:before {
    content: "\e949";
}

.icon-images:before {
    content: "\e94a";
}

.icon-camera:before {
    content: "\e94b";
}

.icon-headphones:before {
    content: "\e94c";
}

.icon-music:before {
    content: "\e94d";
}

.icon-play:before {
    content: "\e94e";
}

.icon-film:before {
    content: "\e94f";
}

.icon-video-camera:before {
    content: "\e950";
}

.icon-dice:before {
    content: "\e951";
}

.icon-pacman:before {
    content: "\e952";
}

.icon-spades:before {
    content: "\e953";
}

.icon-clubs:before {
    content: "\e954";
}

.icon-diamonds:before {
    content: "\e955";
}

.icon-bullhorn:before {
    content: "\e956";
}

.icon-connection:before {
    content: "\e957";
}

.icon-podcast:before {
    content: "\e958";
}

.icon-feed:before {
    content: "\e959";
}

.icon-mic:before {
    content: "\e95a";
}

.icon-book:before {
    content: "\e95b";
}

.icon-books:before {
    content: "\e95c";
}

.icon-library:before {
    content: "\e95d";
}

.icon-file-text:before {
    content: "\e95e";
}

.icon-profile:before {
    content: "\e95f";
}

.icon-file-empty:before {
    content: "\e960";
}

.icon-files-empty:before {
    content: "\e961";
}

.icon-file-text2:before {
    content: "\e962";
}

.icon-file-picture:before {
    content: "\e963";
}

.icon-file-music:before {
    content: "\e964";
}

.icon-file-play:before {
    content: "\e965";
}

.icon-file-video:before {
    content: "\e966";
}

.icon-file-zip:before {
    content: "\e967";
}

.icon-copy:before {
    content: "\e968";
}

.icon-paste:before {
    content: "\e969";
}

.icon-stack:before {
    content: "\e96a";
}

.icon-folder:before {
    content: "\e96b";
}

.icon-folder-open:before {
    content: "\e96c";
}

.icon-folder-plus:before {
    content: "\e96d";
}

.icon-folder-minus:before {
    content: "\e96e";
}

.icon-folder-download:before {
    content: "\e96f";
}

.icon-folder-upload:before {
    content: "\e970";
}

.icon-price-tag:before {
    content: "\e971";
}

.icon-price-tags:before {
    content: "\e972";
}

.icon-barcode:before {
    content: "\e973";
}

.icon-qrcode:before {
    content: "\e974";
}

.icon-ticket:before {
    content: "\e975";
}

.icon-cart:before {
    content: "\e976";
}

.icon-coin-dollar:before {
    content: "\e977";
}

.icon-coin-euro:before {
    content: "\e978";
}

.icon-coin-pound:before {
    content: "\e979";
}

.icon-coin-yen:before {
    content: "\e97a";
}

.icon-credit-card:before {
    content: "\e97b";
}

.icon-calculator:before {
    content: "\e97c";
}

.icon-lifebuoy:before {
    content: "\e97d";
}

.icon-phone:before {
    content: "\e97e";
}

.icon-phone-hang-up:before {
    content: "\e97f";
}

.icon-address-book:before {
    content: "\e980";
}

.icon-envelop:before {
    content: "\e981";
}

.icon-pushpin:before {
    content: "\e982";
}

.icon-location:before {
    content: "\e983";
}

.icon-location2:before {
    content: "\e984";
}

.icon-compass:before {
    content: "\e985";
}

.icon-compass2:before {
    content: "\e986";
}

.icon-map:before {
    content: "\e987";
}

.icon-map2:before {
    content: "\e988";
}

.icon-history:before {
    content: "\e989";
}

.icon-clock:before {
    content: "\e98a";
}

.icon-clock2:before {
    content: "\e98b";
}

.icon-alarm:before {
    content: "\e98c";
}

.icon-bell:before {
    content: "\e98d";
}

.icon-stopwatch:before {
    content: "\e98e";
}

.icon-calendar:before {
    content: "\e98f";
}

.icon-printer:before {
    content: "\e990";
}

.icon-keyboard:before {
    content: "\e991";
}

.icon-display:before {
    content: "\e992";
}

.icon-laptop:before {
    content: "\e993";
}

.icon-mobile:before {
    content: "\e994";
}

.icon-mobile2:before {
    content: "\e995";
}

.icon-tablet:before {
    content: "\e996";
}

.icon-tv:before {
    content: "\e997";
}

.icon-drawer:before {
    content: "\e998";
}

.icon-drawer2:before {
    content: "\e999";
}

.icon-box-add:before {
    content: "\e99a";
}

.icon-box-remove:before {
    content: "\e99b";
}

.icon-download:before {
    content: "\e99c";
}

.icon-upload:before {
    content: "\e99d";
}

.icon-floppy-disk:before {
    content: "\e99e";
}

.icon-drive:before {
    content: "\e99f";
}

.icon-database:before {
    content: "\e9a0";
}

.icon-undo:before {
    content: "\e9a1";
}

.icon-redo:before {
    content: "\e9a2";
}

.icon-undo2:before {
    content: "\e9a3";
}

.icon-redo2:before {
    content: "\e9a4";
}

.icon-forward:before {
    content: "\e9a5";
}

.icon-reply:before {
    content: "\e9a6";
}

.icon-bubble:before {
    content: "\e9a7";
}

.icon-bubbles:before {
    content: "\e9a8";
}

.icon-bubbles2:before {
    content: "\e9a9";
}

.icon-bubble2:before {
    content: "\e9aa";
}

.icon-bubbles3:before {
    content: "\e9ab";
}

.icon-bubbles4:before {
    content: "\e9ac";
}

.icon-user:before {
    content: "\e9ad";
}

.icon-users:before {
    content: "\e9ae";
}

.icon-user-plus:before {
    content: "\e9af";
}

.icon-user-minus:before {
    content: "\e9b0";
}

.icon-user-check:before {
    content: "\e9b1";
}

.icon-user-tie:before {
    content: "\e9b2";
}

.icon-quotes-left:before {
    content: "\e9b3";
}

.icon-quotes-right:before {
    content: "\e9b4";
}

.icon-hour-glass:before {
    content: "\e9b5";
}

.icon-spinner:before {
    content: "\e9b6";
}

.icon-spinner2:before {
    content: "\e9b7";
}

.icon-spinner3:before {
    content: "\e9b8";
}

.icon-spinner4:before {
    content: "\e9b9";
}

.icon-spinner5:before {
    content: "\e9ba";
}

.icon-spinner6:before {
    content: "\e9bb";
}

.icon-spinner7:before {
    content: "\e9bc";
}

.icon-spinner8:before {
    content: "\e9bd";
}

.icon-spinner9:before {
    content: "\e9be";
}

.icon-spinner10:before {
    content: "\e9bf";
}

.icon-spinner11:before {
    content: "\e9c0";
}

.icon-binoculars:before {
    content: "\e9c1";
}

.icon-search:before {
    content: "\e9c2";
}

.icon-zoom-in:before {
    content: "\e9c3";
}

.icon-zoom-out:before {
    content: "\e9c4";
}

.icon-enlarge:before {
    content: "\e9c5";
}

.icon-shrink:before {
    content: "\e9c6";
}

.icon-enlarge2:before {
    content: "\e9c7";
}

.icon-shrink2:before {
    content: "\e9c8";
}

.icon-key:before {
    content: "\e9c9";
}

.icon-key2:before {
    content: "\e9ca";
}

.icon-lock:before {
    content: "\e9cb";
}

.icon-unlocked:before {
    content: "\e9cc";
}

.icon-wrench:before {
    content: "\e9cd";
}

.icon-equalizer:before {
    content: "\e9ce";
}

.icon-equalizer2:before {
    content: "\e9cf";
}

.icon-cog:before {
    content: "\e9d0";
}

.icon-cogs:before {
    content: "\e9d1";
}

.icon-hammer:before {
    content: "\e9d2";
}

.icon-magic-wand:before {
    content: "\e9d3";
}

.icon-aid-kit:before {
    content: "\e9d4";
}

.icon-bug:before {
    content: "\e9d5";
}

.icon-pie-chart:before {
    content: "\e9d6";
}

.icon-stats-dots:before {
    content: "\e9d7";
}

.icon-stats-bars:before {
    content: "\e9d8";
}

.icon-stats-bars2:before {
    content: "\e9d9";
}

.icon-trophy:before {
    content: "\e9da";
}

.icon-gift:before {
    content: "\e9db";
}

.icon-glass:before {
    content: "\e9dc";
}

.icon-glass2:before {
    content: "\e9dd";
}

.icon-mug:before {
    content: "\e9de";
}

.icon-spoon-knife:before {
    content: "\e9df";
}

.icon-leaf:before {
    content: "\e9e0";
}

.icon-rocket:before {
    content: "\e9e1";
}

.icon-meter:before {
    content: "\e9e2";
}

.icon-meter2:before {
    content: "\e9e3";
}

.icon-hammer2:before {
    content: "\e9e4";
}

.icon-fire:before {
    content: "\e9e5";
}

.icon-lab:before {
    content: "\e9e6";
}

.icon-magnet:before {
    content: "\e9e7";
}

.icon-bin:before {
    content: "\e9e8";
}

.icon-bin2:before {
    content: "\e9e9";
}

.icon-briefcase:before {
    content: "\e9ea";
}

.icon-airplane:before {
    content: "\e9eb";
}

.icon-truck:before {
    content: "\e9ec";
}

.icon-road:before {
    content: "\e9ed";
}

.icon-accessibility:before {
    content: "\e9ee";
}

.icon-target:before {
    content: "\e9ef";
}

.icon-shield:before {
    content: "\e9f0";
}

.icon-power:before {
    content: "\e9f1";
}

.icon-switch:before {
    content: "\e9f2";
}

.icon-power-cord:before {
    content: "\e9f3";
}

.icon-clipboard:before {
    content: "\e9f4";
}

.icon-list-numbered:before {
    content: "\e9f5";
}

.icon-list:before {
    content: "\e9f6";
}

.icon-list2:before {
    content: "\e9f7";
}

.icon-tree:before {
    content: "\e9f8";
}

.icon-menu:before {
    content: "\e9f9";
}

.icon-menu2:before {
    content: "\e9fa";
}

.icon-menu3:before {
    content: "\e9fb";
}

.icon-menu4:before {
    content: "\e9fc";
}

.icon-cloud:before {
    content: "\e9fd";
}

.icon-cloud-download:before {
    content: "\e9fe";
}

.icon-cloud-upload:before {
    content: "\e9ff";
}

.icon-cloud-check:before {
    content: "\ea00";
}

.icon-download2:before {
    content: "\ea01";
}

.icon-upload2:before {
    content: "\ea02";
}

.icon-download3:before {
    content: "\ea03";
}

.icon-upload3:before {
    content: "\ea04";
}

.icon-sphere:before {
    content: "\ea05";
}

.icon-earth:before {
    content: "\ea06";
}

.icon-link:before {
    content: "\ea07";
}

.icon-flag:before {
    content: "\ea08";
}

.icon-attachment:before {
    content: "\ea09";
}

.icon-eye:before {
    content: "\ea0a";
}

.icon-eye-plus:before {
    content: "\ea0b";
}

.icon-eye-minus:before {
    content: "\ea0c";
}

.icon-eye-blocked:before {
    content: "\ea0d";
}

.icon-bookmark:before {
    content: "\ea0e";
}

.icon-bookmarks:before {
    content: "\ea0f";
}

.icon-sun:before {
    content: "\ea10";
}

.icon-contrast:before {
    content: "\ea11";
}

.icon-brightness-contrast:before {
    content: "\ea12";
}

.icon-star-empty:before {
    content: "\ea13";
}

.icon-star-half:before {
    content: "\ea14";
}

.icon-star-full:before {
    content: "\ea15";
}

.icon-heart:before {
    content: "\ea16";
}

.icon-heart-broken:before {
    content: "\ea17";
}

.icon-man:before {
    content: "\ea18";
}

.icon-woman:before {
    content: "\ea19";
}

.icon-man-woman:before {
    content: "\ea1a";
}

.icon-happy:before {
    content: "\ea1b";
}

.icon-happy2:before {
    content: "\ea1c";
}

.icon-smile:before {
    content: "\ea1d";
}

.icon-smile2:before {
    content: "\ea1e";
}

.icon-tongue:before {
    content: "\ea1f";
}

.icon-tongue2:before {
    content: "\ea20";
}

.icon-sad:before {
    content: "\ea21";
}

.icon-sad2:before {
    content: "\ea22";
}

.icon-wink:before {
    content: "\ea23";
}

.icon-wink2:before {
    content: "\ea24";
}

.icon-grin:before {
    content: "\ea25";
}

.icon-grin2:before {
    content: "\ea26";
}

.icon-cool:before {
    content: "\ea27";
}

.icon-cool2:before {
    content: "\ea28";
}

.icon-angry:before {
    content: "\ea29";
}

.icon-angry2:before {
    content: "\ea2a";
}

.icon-evil:before {
    content: "\ea2b";
}

.icon-evil2:before {
    content: "\ea2c";
}

.icon-shocked:before {
    content: "\ea2d";
}

.icon-shocked2:before {
    content: "\ea2e";
}

.icon-baffled:before {
    content: "\ea2f";
}

.icon-baffled2:before {
    content: "\ea30";
}

.icon-confused:before {
    content: "\ea31";
}

.icon-confused2:before {
    content: "\ea32";
}

.icon-neutral:before {
    content: "\ea33";
}

.icon-neutral2:before {
    content: "\ea34";
}

.icon-hipster:before {
    content: "\ea35";
}

.icon-hipster2:before {
    content: "\ea36";
}

.icon-wondering:before {
    content: "\ea37";
}

.icon-wondering2:before {
    content: "\ea38";
}

.icon-sleepy:before {
    content: "\ea39";
}

.icon-sleepy2:before {
    content: "\ea3a";
}

.icon-frustrated:before {
    content: "\ea3b";
}

.icon-frustrated2:before {
    content: "\ea3c";
}

.icon-crying:before {
    content: "\ea3d";
}

.icon-crying2:before {
    content: "\ea3e";
}

.icon-point-up:before {
    content: "\ea3f";
}

.icon-point-right:before {
    content: "\ea40";
}

.icon-point-down:before {
    content: "\ea41";
}

.icon-point-left:before {
    content: "\ea42";
}

.icon-warning:before {
    content: "\ea43";
}

.icon-notification:before {
    content: "\ea44";
}

.icon-question:before {
    content: "\ea45";
}

.icon-plus:before {
    content: "\ea46";
}

.icon-minus:before {
    content: "\ea47";
}

.icon-info:before {
    content: "\ea48";
}

.icon-cancel-circle:before {
    content: "\ea49";
}

.icon-blocked:before {
    content: "\ea4a";
}

.icon-cross:before {
    content: "\ea4b";
}

.icon-checkmark:before {
    content: "\ea4c";
}

.icon-checkmark2:before {
    content: "\ea4d";
}

.icon-spell-check:before {
    content: "\ea4e";
}

.icon-enter:before {
    content: "\ea4f";
}

.icon-exit:before {
    content: "\ea50";
}

.icon-play2:before {
    content: "\ea51";
}

.icon-pause:before {
    content: "\ea52";
}

.icon-stop:before {
    content: "\ea53";
}

.icon-previous:before {
    content: "\ea54";
}

.icon-next:before {
    content: "\ea55";
}

.icon-backward:before {
    content: "\ea56";
}

.icon-forward2:before {
    content: "\ea57";
}

.icon-play3:before {
    content: "\ea58";
}

.icon-pause2:before {
    content: "\ea59";
}

.icon-stop2:before {
    content: "\ea5a";
}

.icon-backward2:before {
    content: "\ea5b";
}

.icon-forward3:before {
    content: "\ea5c";
}

.icon-first:before {
    content: "\ea5d";
}

.icon-last:before {
    content: "\ea5e";
}

.icon-previous2:before {
    content: "\ea5f";
}

.icon-next2:before {
    content: "\ea60";
}

.icon-eject:before {
    content: "\ea61";
}

.icon-volume-high:before {
    content: "\ea62";
}

.icon-volume-medium:before {
    content: "\ea63";
}

.icon-volume-low:before {
    content: "\ea64";
}

.icon-volume-mute:before {
    content: "\ea65";
}

.icon-volume-mute2:before {
    content: "\ea66";
}

.icon-volume-increase:before {
    content: "\ea67";
}

.icon-volume-decrease:before {
    content: "\ea68";
}

.icon-loop:before {
    content: "\ea69";
}

.icon-loop2:before {
    content: "\ea6a";
}

.icon-infinite:before {
    content: "\ea6b";
}

.icon-shuffle:before {
    content: "\ea6c";
}

.icon-arrow-up-left:before {
    content: "\ea6d";
}

.icon-arrow-up:before {
    content: "\ea6e";
}

.icon-arrow-up-right:before {
    content: "\ea6f";
}

.icon-arrow-right:before {
    content: "\ea70";
}

.icon-arrow-down-right:before {
    content: "\ea71";
}

.icon-arrow-down:before {
    content: "\ea72";
}

.icon-arrow-down-left:before {
    content: "\ea73";
}

.icon-arrow-left:before {
    content: "\ea74";
}

.icon-arrow-up-left2:before {
    content: "\ea75";
}

.icon-arrow-up2:before {
    content: "\ea76";
}

.icon-arrow-up-right2:before {
    content: "\ea77";
}

.icon-arrow-right2:before {
    content: "\ea78";
}

.icon-arrow-down-right2:before {
    content: "\ea79";
}

.icon-arrow-down2:before {
    content: "\ea7a";
}

.icon-arrow-down-left2:before {
    content: "\ea7b";
}

.icon-arrow-left2:before {
    content: "\ea7c";
}

.icon-circle-up:before {
    content: "\ea7d";
}

.icon-circle-right:before {
    content: "\ea7e";
}

.icon-circle-down:before {
    content: "\ea7f";
}

.icon-circle-left:before {
    content: "\ea80";
}

.icon-checkbox-checked:before {
    content: "\ea8e";
}

.icon-checkbox-unchecked:before {
    content: "\ea8f";
}

.icon-radio-checked:before {
    content: "\ea90";
}

.icon-radio-checked2:before {
    content: "\ea91";
}

.icon-radio-unchecked:before {
    content: "\ea92";
}

.icon-crop:before {
    content: "\ea93";
}

.icon-insert-template:before {
    content: "\eaae";
}

.icon-paragraph-left:before {
    content: "\eab3";
}

.icon-paragraph-center:before {
    content: "\eab4";
}

.icon-paragraph-right:before {
    content: "\eab5";
}

.icon-paragraph-justify:before {
    content: "\eab6";
}

.icon-indent-increase:before {
    content: "\eab7";
}

.icon-indent-decrease:before {
    content: "\eab8";
}

.icon-share:before {
    content: "\eab9";
}

.icon-new-tab:before {
    content: "\eaba";
}

.icon-embed:before {
    content: "\eabb";
}

.icon-embed2:before {
    content: "\eabc";
}

.icon-terminal:before {
    content: "\eabd";
}

.icon-share2:before {
    content: "\eabe";
}

.icon-mail:before {
    content: "\eabf";
}

.icon-mail2:before {
    content: "\eac0";
}

.icon-mail3:before {
    content: "\eac1";
}

.icon-mail4:before {
    content: "\eac2";
}

.icon-amazon:before {
    content: "\eac3";
}

.icon-google:before {
    content: "\eac4";
}

.icon-google2:before {
    content: "\eac5";
}

.icon-google3:before {
    content: "\eac6";
}

.icon-google-plus:before {
    content: "\eac7";
}

.icon-google-plus2:before {
    content: "\eac8";
}

.icon-google-plus3:before {
    content: "\eac9";
}

.icon-hangouts:before {
    content: "\eaca";
}

.icon-google-drive:before {
    content: "\eacb";
}

.icon-facebook:before {
    content: "\eacc";
}

.icon-facebook2:before {
    content: "\eacd";
}

.icon-instagram:before {
    content: "\eace";
}

.icon-whatsapp:before {
    content: "\eacf";
}

.icon-spotify:before {
    content: "\ead0";
}

.icon-telegram:before {
    content: "\ead1";
}

.icon-twitter:before {
    content: "\ead2";
}

.icon-vine:before {
    content: "\ead3";
}

.icon-rss:before {
    content: "\ead7";
}

.icon-youtube:before {
    content: "\ead9";
}

.icon-soundcloud:before {
    content: "\eaff";
}

.icon-skype:before {
    content: "\eb01";
}

.icon-pinterest:before {
    content: "\eb0d";
}

.icon-pinterest2:before {
    content: "\eb0e";
}

.icon-xing2:before {
    content: "\eb10";
}

.icon-paypal:before {
    content: "\eb14";
}

a:focus {
    outline: none;
}

p {
    font-size: 1em;
    font-weight: 400;
    line-height: 1.4em;
}

p {
    font-size: 1em;
    font-weight: 400;
    line-height: 1.8em;
    margin: 0 0 1.618em;
}

.archive #nc_wishlist_added {
    display: none;
}

/*body.tax-product_cat .product .added{
    display: none !important;
}*/

.product .added {
    display: none !important;
}

.added_to_cart.wc-forward {
    background: black !important;
}

/* Reusable column setup */

.col {
    border: 0px solid rgba(0, 0, 0, 0);
    float: left;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -moz-background-clip: padding-box !important;
    -webkit-background-clip: padding-box !important;
    background-clip: padding-box !important;
}

#error404 .inner {
    background: url(design/banner-404.png) center top;
    background-size: cover;
    min-height: 600px;
}

#error404 .inner p {
    padding: 10px 20px;
    background: #fff;
    display: inline-block;
}

#error404 form {
    display: inline-block;
    position: absolute;
    bottom: 10%;
    right: 5%;
}

#error404 .banner .slider-title {
    padding-top: 20px;
    margin-bottom: 40px;
    bottom: auto;
    position: relative;
}

.col {
    margin-left: 2%;
    padding: 0 1.5%;
}

.row .col:first-child {
    margin-left: 0;
}

.span_1 {
    width: 6.5%;
}

.span_2 {
    width: 15.0%;
}

.span_3 {
    width: 23.5%;
}

.span_4 {
    width: 32.0%;
}

.span_5 {
    width: 40.5%;
}

.span_6 {
    width: 49.0%;
}

.span_7 {
    width: 57.5%;
}

.span_8 {
    width: 66.0%;
}

.span_9 {
    width: 74.5%;
}

.span_10 {
    width: 83.0%;
}

.span_11 {
    width: 91.5%;
}

.span_12 {
    margin-left: 0;
    width: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6,
body,
button,
input,
textarea {
    font-family: 'Titillium Web', sans-serif;
    color: #000;
    margin: 0;
}

a {
    color: #000;
}


/*
.product-categories .cat-item {
	display: none;
}
.product-categories .cat-item.current-cat, .product-categories .cat-item.current-cat .children .cat-item{
	display: block;
}
*/

.easy {
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.symbol {
    display: block;
    margin: 30px auto;
    text-align: center;
}

.symbol:before {
    padding: 8px;
    font-size: 1em;
    display: inline-block;
    background-color: #000;
    color: #fff;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.symbol:hover:before {
    background-color: #eb2944;
}

.col-full {
    max-width: 95%;
    margin: 0 5% 0 0;
    position: relative;
}

.header-full {
    margin-right: 5%;
    display: inline-block;
    width: 95%;
    margin-top: 0;
}

#container {
    max-width: 1680px;
    position: relative;
    margin: 0 auto;
    padding: 0;
}

#brand {
    width: 160px;
    position: fixed;
    background: url(design/bg-logo.png) center top no-repeat;
    background-size: 100%;
}

#brand .brand {
    display: inline-block;
    margin-bottom: 30px;
    width: 100%;
}

#brand .inner {
    padding: 10px 30px 40px 30px;
}

#page {
    padding-left: 160px;
}

.order_item .product-name a {
    font-size: 1.4em;
}

table.order_details tbody tr:last-child td {
    border-bottom: 5px solid #000;
}

table.order_details .order_item .product-name img {
    max-width: 100px;
    float: left;
    margin-right: 20px;
    margin-bottom: 0;
}

.minha-conta {
    margin-top: 20px;
}

.share {
    margin-top: 20px;
}

.horarios {
    background: #000;
    color: #fff;
    font-size: 0.88em;
    font-weight: 400;
    text-transform: uppercase;
    margin-bottom: 35px;
}


/*
.single-product div.product table.variations .label label{
	display: none;
}
.single-product div.product table.variations .label:after{
	content: "Tamanhos";
	display: block;
}
*/

.single-product div.product table.variations .value div {
    display: inline-block;
}

.variations input[type="radio"] {
    display: none;
}

.variations .value label,
.widget-area .widget.widget_layered_nav ul li a {
    display: inline-block;
    padding: 0 5px;
    font-size: 1.1em;
    margin: 2px;
    min-width: 30px;
    height: 30px;
    text-align: center;
    line-height: 1.6em;
    border: 2px solid #dedede;
    border-radius: 0;
    background-color: #fff;
    color: #000;
    font-weight: 700;
    cursor: pointer;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.widget-area .widget.widget_layered_nav ul li a {
    line-height: 1.9em;
}

.widget-area .widget.widget_layered_nav ul li {
    width: auto;
    display: inline-block;
    float: left;
}

.variations .value label:hover,
.widget-area .widget.widget_layered_nav ul li a:hover {
    background-color: #fff;
    color: #000;
    border: 2px solid #000;
}

.variations .value input[type="radio"]:checked+label,
.widget-area .widget.widget_layered_nav ul li.chosen a {
    border: 2px solid #000;
}

.widget-area .widget.widget_layered_nav ul li.chosen a {
    background: #000;
    color: #fff;
}

.variations .value label[for="pa_cor_v_vermelha"] {
    background-color: #ff0000;
    text-indent: -99999px;
}

.variations .value label[for="pa_cor_v_branca"] {
    background-color: #fff;
    text-indent: -99999px;
}

.widget_layered_nav li:before,
.widget_layered_nav_filters ul li.chosen a:before,
.widget_product_categories ul li:before,
.widget.woocommerce li .count,
.widget.woocommerce li span {
    display: none;
    visibility: hidden;
}


/*
	.widget_layered_nav.widget-color ul li a:after{
		display: block;
		width: 20px;
	    height: 20px;
	    margin: 4px;
		content: "";
		border: 1px solid #eee;
		background: #fff;
		float: left;
		position: relative;
		left: -5px;
	}
	.widget_layered_nav.widget-color ul li:first-child a:after{
		background: #000;
	}
	.widget_layered_nav.widget-color ul li:last-child a:after{
		background: #ff0000;
	}
	*/

.horarios .inner {
    padding: 25px;
}

.header-full .site-logo-link {
    display: none;
}

.home-novidades {
    margin-top: 65px;
}

.page-template-template-homepage .header-full .fixedmenu {
    display: none;
}

.page-template-template-homepage .storefront-product-section {
    margin-bottom: 0;
}

.home-institucional .fixedmenu .col-full {
    margin: 0;
    width: 100%;
    max-width: 100%;
}

.page-template-template-homepage .fixedmenu {
    position: relative;
}

.home-institucional .main-navigation ul li,
.home-institucional .secondary-navigation ul li,
.home-institucional .secondary-navigation ul.menu>li>a,
.home-institucional .secondary-navigation ul.nav-menu>li>a {
    width: 100%;
    color: #000;
    background: #fff;
    text-align: left;
    float: none;
}

.home-institucional .secondary-navigation ul.menu>li>a,
.home-institucional .secondary-navigation ul.nav-menu>li>a {
    padding: 10px 25px;
    font-size: 1em;
}

.main-navigation #menu-mobile>li>a,
.main-navigation #menu-mobile>li>a {
    font-size: 1.2em;
    line-height: 1.3em;
    padding: 6px 5%;
    border-bottom: 1px solid;
}

.page-template-template-fullwidth-php .site-main .columns-4 ul.products li.product,
.page-template-template-homepage-php .site-main .columns-4 ul.products li.product,
.storefront-full-width-content .site-main .columns-4 ul.products li.product,
.site-main ul.products li.product {
    width: 20%;
    float: left;
    margin-right: 6.6%;
}

.page-template-template-homepage-php .site-main .columns-4 ul.products li.product {
    margin-bottom: 20px;
    margin-right: 4%;
    width: 22%;
}

.page-template-template-homepage-php ul.products li.product img {
    margin: 0;
}

ul.products li.product .outher {
    position: relative;
    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;
    z-index: 2;
    transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
}

ul.products li.product .outher:before {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    background-color: rgba(235, 40, 65, 0);
    top: 0;
    left: 0;
    z-index: 1;
    transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    -ms-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
}

ul.products li.product .outher h3 {
    position: absolute;
    top: 50%;
    z-index: 4;
    text-align: center;
    opacity: 0;
    min-height: 0;
    color: #fff;
    font-size: 1.2em;
    line-height: 1em;
    font-weight: 700;
    margin: 0 5%;
    width: 90%;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
}

.page-template-template-homepage-php ul.products li.product .price,
.page-template-template-homepage-php ul.products li.product .price .from,
ul.products li.product .star-rating {
    margin: 10px 0 0 0;
    font-size: 1.4em;
    color: #fff;
    font-weight: 400;
}

.page-template-template-homepage-php ul.products li.product .price .from {
    font-size: .7em;
    margin-bottom: .5em;
    display: block;
}

ul.products li.product:hover .outher:before {
    background-color: rgba(235, 40, 65, 0.8);
}

ul.products li.product:hover .outher h3 {
    opacity: 1;
}

.left-sidebar .widget-area {
    width: 22%;
    max-width: 260px;
    float: left;
    margin-right: 5%;
}

.left-sidebar .content-area {
    width: 73%;
    float: right;
    margin-right: 0;
}

.left-sidebar .coluna-esquerda {
    width: 30%;
    max-width: 260px;
    float: left;
    margin-right: 5%;
}

.left-sidebar .conteudo-direito {
    width: 65%;
    float: right;
    margin-right: 0;
}

.widget.widget_price_filter {
    min-height: 130px;
    position: relative;
}

.widget_price_filter .price_slider_amount .button {
    width: 100%;
    position: relative;
    top: 35px;
}

.widget_price_filter .price_slider_amount .price_label {
    width: 100%;
    position: relative;
    top: -35px;
    text-align: left;
}

div.wpcf7-response-output {
    font-weight: 700;
    padding: 0.5em 1em;
    color: #fff;
}

div.wpcf7-mail-sent-ok {
    border: 2px solid #097dc9;
    background: #097dc9;
    color: #fff;
}

div.wpcf7-mail-sent-ng {
    border: 2px solid #eb2944;
    background: #eb2944;
    color: #fff;
}

div.wpcf7-spam-blocked {
    border: 2px solid #ffa700;
    background: #ffa700;
    color: #fff;
}

div.wpcf7-validation-errors {
    border: 2px solid #eb2944;
    background: #eb2944;
    color: #fff;
}

.woocommerce .woocommerce-ordering {
    position: relative;
}

.woocommerce-result-count {
    padding: 5px 10px;
    text-transform: uppercase;
}

.woocommerce .woocommerce-ordering select,
.woocommerce select {
    font-family: 'Titillium Web', sans-serif;
    position: relative;
    display: inline-block;
    margin: 0;
    font-size: 1em;
    font-weight: 700;
    padding: 2px 35px 2px 10px;
    line-height: 1.8em;
    height: 28px;
    color: #000;
    cursor: pointer;
    outline: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
    background: #fff url(design/seta-select.svg) right center no-repeat;
    background-size: 22px 22px;
    border-radius: 0;
    border: 1px solid #000;
    text-transform: uppercase;
}

.lps #submenu {
    display: none;
}

/*Código para ajustar widget quando a categoria for LPs*/

body.woocommerce-page .widget-area #block-4.widget {
    display: none;
}

body.woocommerce-page.lps .widget-area #block-4.widget {
    display: block !important;
}

ul.order_details li.bank_image {
    font-size: 0;
    margin: 0;
    padding: 0;
}

input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
textarea,
.input-text,
.input-text.qty,
table.cart .quantity .qty,
#wc-correios-simulator .cart #zipcode {
    font-weight: 700;
    color: #000;
    line-height: 2em;
    padding: 6px 11px;
    background-color: #f5f5f5;
    color: #000;
    outline: none;
    border: 1px solid #b3b3b3;
    -webkit-appearance: none;
    border-radius: 0;
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
textarea:focus,
.input-text.qty:focus,
table.cart .quantity .qty:focus,
#wc-correios-simulator .cart #zipcode:focus {
    border: 2px solid #000;
    color: #000;
    background-color: #fff;
    padding: 5px 10px;
}

.single-product div.product form.cart .button {
    float: left;
    margin: 0 0 0 10px;
    font-size: 1.8em;
    letter-spacing: 0.025em;
    height: 46px;
    padding: 0 12%;
}

.input-text.qty,
table.cart .quantity .qty,
#wc-correios-simulator .cart #zipcode {
    height: 46px;
    color: #000;
    font-size: 1.5em;
    line-height: 1em;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.125);
}

table.cart td.actions input {
    height: 46px;
}

#wc-correios-simulator .cart #zipcode {
    line-height: 2em;
    max-width: none;
}

#wc-correios-simulator {
    width: 100%;
}

#wc-correios-simulator #zipcode {
    max-width: none;
    float: left;
}

.single-product div.product .variations_button,
.single-product div.product form.cart {
    border: none;
}

/* Newsletter footer CF7 */
span.wpcf7-not-valid-tip {
    display: none;
}

div.wpcf7-response-output {
    margin: 2em 0 1em;
}

.page-id-7393 footer .newsletter {
    display: none;
}

.cadastro input[type="text"],
.cadastro input[type="email"],
.cadastro input[type="url"],
.cadastro input[type="password"],
.cadastro textarea,
.cadastro .input-text {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    width: 100%;
    max-width: 100%;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    border: 1px solid #000;
    background: #fff;
    margin: 0 0 16px 0;
    padding: 4px 10px;
    border-radius: 0;
    font-size: 12px;
    line-height: 14px;
    height: 38px;
}

.cadastro textarea {
    height: 100px;
    max-height: 100px;
    padding: 10px;
}

.cadastro input[type="submit"] {
    overflow: visible;
    position: relative;
    float: right;
    border: 0;
    padding: 10px 15px;
    cursor: pointer;
    height: 40px;
    min-width: 20%;
    font-size: 0.88em;
    color: #fff;
    text-transform: uppercase;
    background: #eb2944;
    /*margin: 10px 0;*/
    margin: -17px 0 0 0;
    box-shadow: none;
}

.cadastro {
    padding: 20px;
    background: #000;
    color: #fff;
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-bottom: 40px;
}

.cadastro.newsletter h4,
.cadastro.cadastro-excursoes h4 {
    color: #fff;
    font-size: 1.2em;
    text-transform: uppercase;
}

.cadastro.cadastro-excursoes h4 {
    color: #000;
    font-size: 1.2em;
    margin-bottom: 50px;
}

.cadastro.cadastro-excursoes h3 {
    color: #000;
    font-size: 2.2em;
    margin-bottom: 10px;
    border-bottom: 1px solid;
}

.cadastro p {
    color: #fff;
    margin: 0;
}

.cadastro form {
    margin-bottom: 1.618em;
    display: inline-block;
    width: 70%;
    margin: 10px 15% 0;
}

.cadastro.newsletter input[type="email"] {
    width: 80%;
    height: 40px;
    padding: 10px 14px;
    float: left;
    font-size: 0.88em;
    line-height: 2em;
    border: 0;
    background: #fff;
    margin: 10px 0;
}

.cadastro button {
    overflow: visible;
    position: relative;
    float: left;
    border: 0;
    padding: 10px 15px;
    cursor: pointer;
    height: 40px;
    width: 20%;
    font-size: 0.88em;
    color: #fff;
    text-transform: uppercase;
    background: #eb2944;
    margin: 10px 0;
    box-shadow: none;
}

.cadastro button:hover {
    background-color: #cb1f36
}

.cadastro.cadastro-excursoes {
    background: #EAEAEA;
    padding: 50px;
}

.cadastro.cadastro-excursoes .row {
    border-bottom: 1px solid #F9F9F9;
    display: inline-block;
    width: 100%;
}

.cadastro.cadastro-excursoes label {
    color: #000;
    font-size: 0.85em;
    line-height: 2em;
    font-weight: normal;
    text-transform: uppercase;
    margin: 5px 0 0 0;
    width: 30%;
    float: left;
    text-align: left;
}

.cadastro.cadastro-excursoes span.wpcf7-form-control-wrap {
    position: relative;
    width: 70%;
    display: inline-block;
    float: right;
}

clearfix:before,
.clearfix:after,
.row:before,
.row:after {
    content: '\0020';
    display: block;
    overflow: hidden;
    visibility: hidden;
    width: 0;
    height: 0;
}

.woocommerce-breadcrumb {
    margin-bottom: 2.26em;
    font-size: .857em;
    padding: 1em 0;
    text-transform: uppercase;
    border-bottom: none;
    text-align: right;
}

.woocommerce-breadcrumb a {
    color: #000;
    font-weight: 700;
    padding: 2px 5px;
}

.woocommerce-breadcrumb a:hover {
    color: #fff;
    background-color: #000;
}

.page-title {
    font-size: 2.46em;
    font-weight: 700;
    letter-spacing: 0.025em;
    margin-bottom: 0.56em;
}

ul.products li.product h3 {
    font-size: 0.88em;
    font-weight: 300;
    text-transform: uppercase;
    line-height: 1.1em;
    color: #000;
    min-height: 40px;
}

.verticalline {
    height: 500px;
    position: relative;
    left: 0;
}

.verticalline h2 {
    background: #fff;
    margin: 0 auto;
    padding: 10px;
    font-size: 1em;
    line-height: 1em;
    text-align: center;
    position: relative;
    top: 25%;
}

.verticalline:after {
    content: "";
    position: absolute;
    height: 500px;
    z-index: -1;
    top: 0;
    bottom: 0;
    left: 50%;
    border-left: 1px solid #000;
}

.home-institucional {
    width: 15%;
    margin-right: 10%;
    float: left;
}

.home-novidades-titulo {
    width: 10%;
    margin-right: 5%;
    float: left;
}

.novidade {
    width: 60%;
    float: left;
}

.wew-notification-action_wrapper {
    padding: 20px;
    background-color: #000;
    color: #FFF;
    position: relative;
}

.single-product div.product div.wew-notification-action_wrapper .button,
.wew-email-to-notify {
    width: 100%;
    margin: 0;
    height: auto;
    font-size: 1.2rem;
}

.wew-notification-action_wrapper img {
    vertical-align: middle;
    margin-left: 5px;
    display: inline-block;
    position: absolute;
    top: 32px;
    right: 32px;
}

.woocommerce-active .site-header .site-search {
    background: url(design/search-sky.png) right top no-repeat;
    background-size: 100%;
    position: absolute;
    width: 560px;
    height: 110px;
    top: 0;
    right: 0;
    margin: 0;
    z-index: 90;
}

.widget.woocommerce.widget_product_search {
    top: 20px;
    width: 200px;
    left: 65px;
    position: relative;
    margin: 0;
}

.page-template-template-fullwidth-php .site-main .columns-4 ul.products li.product:last-child,
.page-template-template-homepage .site-main .columns-4 ul.products li.product:last-child,
.storefront-full-width-content .site-main .columns-4 ul.products li.product:last-child {
    margin-right: 0 !important;
}

.site-search .widget_product_search input[type=search],
.site-search .widget_product_search input[type=text] {
    background: transparent !important;
    color: #fff;
    font-weight: 700;
    font-family: 'Titillium Web', sans-serif;
    font-size: 1.2em;
    line-height: 1.5em;
    padding: 1em 1em 1em 3em;
    box-shadow: none !important;
    border: none;
}

.widget_search form:before,
.widget_product_search form:before {
    color: #fff;
    font-size: 1.4em;
    top: 18px !important;
    left: 18px !important;
}

.site-header-cart .cart-contents {
    text-align: right;
    border: 2px solid #000;
    padding: 10px;
}

.site-header-cart .cart-contents:after {
    right: auto;
    left: 10px;
    top: 10px;
    font-size: 1.3em;
    position: absolute;
    content: "\f07a" !important;
    line-height: 1;
}

.woocommerce-active .site-header .site-header-cart {
    width: 180px;
    z-index: 909;
    top: 50px;
    right: 5%;
    float: right;
    margin: 0;
    position: absolute;
    padding: 0;
}

.site-header-cart .cart-contents:after {
    right: auto;
    left: 0;
    top: 0;
    font-size: 1.3em;
    color: #fff;
    background: #000;
    padding: 11px;
}

.site-header-cart .widget_shopping_cart {
    background: transparent;
}

.site-header .widget_shopping_cart li {
    display: block;
}

.site-header-cart .widget_shopping_cart .widget_shopping_cart_content {
    background: transparent;
    position: relative;
    right: 260px;
    width: 430px;
    padding-right: 180px;
    top: -45px;
}

.site-header-cart .widget_shopping_cart .widget_shopping_cart_content:after {
    display: block;
    position: absolute;
    content: "";
    top: 12px;
    right: 170px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 0 10px 10px;
    border-color: transparent transparent transparent #000000;
}

.site-header .widget_shopping_cart li,
.site-header .widget_shopping_cart p.buttons,
.site-header .widget_shopping_cart p.total {
    border-right: 4px solid #000;
}

.site-header .widget_shopping_cart li,
.site-header .widget_shopping_cart p.buttons {
    border-bottom: 2px solid #000;
}

.product_list_widget li img {
    max-width: 4.618em;
    float: right;
}

.site-header .widget_shopping_cart li,
.site-header .widget_shopping_cart p.buttons {
    background-color: #fff;
}

.site-header .widget_shopping_cart p.total {
    background-color: #000;
    color: #fff;
}

.content-fullwidth .entry-content.page-banner {
    padding-left: 120px;
}

.content-fullwidth .entry-content {
    padding-left: 0;
}

.banner {
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
    position: relative;
}

.banner .slick-list {
    overflow: hidden;
    padding-bottom: 15px;
}

.banner:before {
    content: "";
    width: 500px;
    height: 60px;
    background: url(design/banner-crop.png) 20px top no-repeat;
    background-size: 100%;
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    z-index: 1;
}

.banner-inner {
    width: 100%;
    position: relative;
}


/*
.banner-foto{
	height: 200px;
	overflow: hidden;
}
.banner-foto img{
	max-width: 110%;
    min-width: 1000px;
}
*/

.banner .slider-title {
    position: absolute;
    width: 50%;
    bottom: -15px;
    z-index: 2;
}

.banner.home .slider-title {
    position: absolute;
    width: 50%;
    bottom: 5%;
    z-index: 2;
}

.banner h1 {
    padding: 0;
    color: #000;
    display: block;
    margin: 0;
    font-size: 2.4em;
    font-weight: 700;
    line-height: 1.36em;
}

.banner h1 span {
    display: inline;
    position: relative;
    padding: 0 20px;
    background-color: #eb2944;
    background-color: rgba(235, 40, 65, 0.9);
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
}

.banner h2 {
    margin: 6px 0 0 0;
    color: #fff;
    background: #000;
    padding: 5px 20px 5px 30%;
    display: inline-block;
    font-size: 1em;
    font-weight: 700;
    text-align: right;
    text-transform: uppercase;
}

.banner.home h1 {
    font-size: 3.4em
}

.banner.home h2 {
    font-size: 1.4em;
}

.banner img {
    padding-left: 120px;
}

.depoimentos.slick-initialized .slick-slide {
    padding: 35px 25px 15px;
    height: auto;
}

.slick-slider {
    margin-bottom: 0;
}

.page-template-template-homepage .slick-slider {
    margin-bottom: 0;
    margin-top: 0;
}

.slick-prev,
.slick-next {
    display: none;
    width: 0;
    height: 0;
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: auto;
}

.slick-dots {
    bottom: 20px;
    margin: 0;
    display: block;
    width: 100%;
    padding: 0 20px 0 140px;
    text-align: right;
}

.slick-dots li {
    width: 15px;
    height: 15px;
    margin: 0 5px;
    padding: 0;
    cursor: pointer;
}

.slick-dots li button {
    background: #fff;
    border-radius: 100%;
    width: 15px;
    height: 15px;
    border: 2px solid #fff;
}

.slick-dots li button:hover {
    background-color: #eb2944;
    background-color: rgba(235, 40, 65, 0.9);
}

.slick-dots li.slick-active button {
    background-color: #000;
}

.slick-dots li button:before {
    display: none;
}

.depoimentos .slick-dots li {
    width: 10px;
    height: 10px;
    margin: 0 2px;
}

.depoimentos .slick-dots li button {
    width: 10px;
    height: 10px;
    padding: 3px;
}

.depoimentos .slick-dots,
.galeria .slick-dots {
    bottom: -30px;
    text-align: center;
    padding: 0;
}

.banner.depoimentos:before {
    display: none;
}

.depoimentos .slick-dots li button,
.galeria .slick-dots li button {
    border: 2px solid #000;
}

.column-content {
    width: 50%;
    float: left;
    display: inline-block;
}

.column-lista {
    width: 30%;
    margin-left: 5%;
    float: left;
    display: inline-block;
}

.column-broken {
    width: 10%;
    margin-left: 5%;
    float: left;
    display: inline-block;
}

.depoimentos-border {
    display: block;
    border: 3px solid #000;
}

.chamada {
    margin-bottom: 80px;
}

.excursoes-lista {
    margin: 0;
}

.excursoes-lista li {
    display: inline-block;
    clear: both;
    width: 100%;
    margin-bottom: 50px;
}

.excursoes-lista li .poster,
.selo-lista {
    width: 40%;
    margin: 2.5%;
    float: left;
    display: inline-block;
}

.excursoes-lista li .info,
.selo-title {
    display: inline-block;
    float: left;
    padding: 20px 0;
    margin: 2.5%;
    width: 40%;
    border-top: 6px solid #000;
}

.selo-title {
    text-align: right;
}

.excursao-data {
    display: none;
    */ font-size: 0.78em;
    display: block;
    padding: 5px 0;
    border-bottom: 2px solid;
    margin-bottom: 5px;
}

.excursoes-lista li .info h3,
.selo-title h3 {
    text-transform: uppercase;
    margin-bottom: 12px;
    font-size: 1.6em
}

.selo-lista li {
    margin-bottom: 12px;
    list-style: none;
}

.ms-slide-info {
    left: -120px;
    position: absolute;
    padding: 0;
    min-height: 0 !important;
    height: auto;
    bottom: 50px !important;
    width: 650px !important;
    margin: 0 auto;
}

.ms-info {
    padding: 0;
    color: #000;
    display: block;
    margin: 0;
    font-size: 3.2em;
    font-weight: 700;
    line-height: 1.2em;
}

.ms-info strong {
    display: inline;
    position: relative;
    padding: 0 20px;
    background-color: #eb2944;
    background-color: rgba(235, 40, 65, 0.9);
    /* Needs prefixing */
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
}

.ms-info em {
    font-size: 0.33em;
    display: inline-block;
    width: 75%;
    background: #000;
    color: #fff;
    font-style: normal;
    font-weight: 700;
    text-align: right;
    line-height: 1.5em;
    position: absolute;
    bottom: -40px;
    left: 0;
    padding: 5px 20px;
    text-transform: uppercase;
}

.hometitle {
    text-align: left;
    padding-bottom: 12px;
    background-size: 200px 4px;
    padding-bottom: 10px;
    margin-bottom: 60px;
    font-weight: 300;
    text-align: center;
}

.hometitle span {
    position: relative;
    font-weight: 200;
    -webkit-font-smoothing: antialiased;
    text-decoration: none;
    font-size: 1.2em;
    line-height: 1em;
    letter-spacing: 0.05em;
    text-align: center;
    display: inline-block;
    margin: 0;
    padding: 10px 20px;
    top: 35px;
    background: #F9F9F9;
    text-transform: uppercase;
}

.hometitle span b {
    font-size: 0.68em;
    font-weight: 200;
}

.hometitle a {
    color: #757575;
}


/* TOP BAR PROMO */
.banner-promo {
    text-align: center;
    margin: 20px auto;
}

.topbarpromo {
    text-align: center;
    color: #fff;
    padding: .66em;
    font-weight: 500;
    background: #9b8b9f;
    background-size: 200px 200px;
}

.topbarpromo span {
    margin: 0 2em;
    font-family: 'Titillium Web', sans-serif;
    font-size: 1em;
    font-weight: 500;
    text-transform: uppercase;
    line-height: 1.4em;
    letter-spacing: 0.08em;
    display: inline-block;
}

.topbarpromo span:before {
    font-family: "FontAwesome";
    margin-right: 0.53em;
    font-size: 1.4em;
    display: inline-block;
    top: 0.1em;
    position: relative;
}

.topbarpromo span.frete:before {
    content: "\f0d1";
}

.topbarpromo span.pagamento:before {
    content: "\f09d";
}

.topbarpromo span.troca:before {
    content: "\f021";
}

.toppromo {
    margin-top: 20px;
}

.lsi-social-icons li {
    display: block !important;
    float: none !important;
    margin: 0 0 10px 0 !important;
}

.progressbar {
    margin: 20px auto;
    padding: 0;
    display: block;
    width: 75%;
    counter-reset: step;
}

.progressbar li {
    list-style-type: none;
    width: 33.33%;
    float: left;
    font-size: 0.85em;
    position: relative;
    text-align: center;
    text-transform: uppercase;
    color: #3a3944;
}

.progressbar li a {
    padding: 50px 10px 10px 10px;
}

.progressbar li:before {
    width: 36px;
    height: 36px;
    content: counter(step);
    counter-increment: step;
    line-height: 30px;
    border: 3px solid #d4d3d6;
    display: block;
    text-align: center;
    margin: 0 auto 10px auto;
    border-radius: 50%;
    background-color: white;
    font-size: 2em;
    color: #d4d3d6;
}

.progressbar li:after {
    width: 100%;
    height: 3px;
    content: '';
    position: absolute;
    background-color: #d4d3d6;
    top: 17px;
    left: -50%;
    z-index: -1;
}

.progressbar li:first-child:after {
    content: none;
}

.progressbar li.ok:before {
    border-color: #3a3944;
    color: #3a3944;
    background-color: #fff;
}

.progressbar li.active:after,
.progressbar li.ok:after {
    background-color: #3a3944;
}

.progressbar li.active:before {
    border-color: #3a3944;
    color: #fff;
    background-color: #3a3944;
    width: 42px;
    height: 42px;
    line-height: 36px;
    top: -3px;
    position: relative;
    margin: 0 auto 4px auto;
}

ul#shipping_method li {
    font-size: 1rem;
}

ul#shipping_method li {
    margin-bottom: 10px;
}

.checkout .shipping th {
    display: none;
}

.woocommerce-shipping-calculator #calc_shipping_city {
    display: none;
}

.steps-set #order_review {
    width: 100%;
}

.steps-set {
    overflow: hidden;
    position: relative;
    height: 900px;
}

.column-step {
    display: inline-block;
    width: 100%;
    float: left;
    display: none;
}

.column-step.active {
    display: block;
}

.steps-mark {
    width: 100%;
    margin: 0 0 40px 0;
    display: inline-block;
}

.checkout-title {
    font-weight: 700;
    font-size: 1.4em;
    text-transform: uppercase;
    border-bottom: 1px solid;
    text-align: center;
    padding-bottom: 0.5em;
    margin-bottom: 2em;
}

.steps-set .col2-set.addresses .col-2.woocommerce-Address {
    border: 1px solid;
    padding: 20px;
    width: 48%;
    margin: 0 1%;
    float: left;
}

.page-template-template-fullwidth-php .col2-set#customer_login .col-1 {
    width: 50%;
    float: left;
    margin: 0;
    padding-right: 5%;
    border-right: 1px solid #000;
}

.page-template-template-fullwidth-php .col2-set#customer_login .col-2 {
    width: 45%;
    float: left;
    margin-left: 5%;
}

.order_details td {
    width: 50%;
}

.steps-set #order_review {
    border: none;
}

@media screen and (max-width: 1200px) {
    .home-institucional {
        width: 22%;
        margin-right: 3%;
    }
}

@media screen and (max-width: 1100px) {
    .woocommerce-active .site-header .site-search {
        background: url(design/search-sky.png) left top no-repeat;
        background-size: 185%;
        position: absolute;
        width: 380px;
        height: 140px;
        top: -42px;
        right: 0;
        margin: 0;
        z-index: 90;
    }

    .widget.woocommerce.widget_product_search {
        top: 42px;
        width: 200px;
        left: 120px;
        position: relative;
        margin: 0;
    }

    .banner:before {
        width: 45%;
        height: 60px;
    }

    .banner .slider-title {
        width: 70%;
    }

    .banner h1 {
        font-size: 1.8em;
    }

    .banner h2 {
        font-size: 0.88em;
    }

    .banner img {
        padding-left: 80px;
    }

    .banner.home h1 {
        font-size: 2.4em;
    }

    .banner.home h2 {
        font-size: 1em;
    }

    .content-fullwidth .entry-content.page-banner {
        padding-left: 80px;
    }

    .hentry .entry-header {
        margin: 0;
    }
}


/* USER */

@media screen and (min-width: 768px) {
    .single-product div.product .summary {
        width: 45%;
    }

    .single-product div.product .images {
        width: 40%;
        margin-right: 5%;
        margin-left: 10%;
    }

    .single-product div.product form.cart {
        margin-bottom: 0;
        padding: 10px 0;
    }

    .caroufredsel_wrapper,
    .yith_magnifier_gallery,
    .flex-control-nav.flex-control-thumbs {
        width: 100% !important;
        text-align: center !important;
        margin: 20px auto 0;
        list-style: none;
    }

    .yith_magnifier_gallery li,
    .flex-control-nav.flex-control-thumbs li {
        width: auto;
        max-width: 120px;
        position: relative;
        float: none;
        margin: 0 10px;
        display: inline-block;
        cursor: pointer;
        padding: 5px;
        -webkit-transition: all 0.2s ease-in-out;
        -moz-transition: all 0.2s ease-in-out;
        -o-transition: all 0.2s ease-in-out;
        -ms-transition: all 0.2s ease-in-out;
        transition: all 0.2s ease-in-out;
    }

    .flex-control-nav.flex-control-thumbs li:hover {
        box-shadow: 0 0 0 2px #000;
    }

    .yith_magnifier_zoom_magnifier {
        border: 5px solid #000;
    }

    .single-product.woocommerce .thumbnails:hover #slider-prev,
    .single-product.woocommerce .thumbnails:hover #slider-next {
        width: 0;
        height: 0;
    }

    .addresses {
        width: 100% !important;
        padding: 0;
        margin: 0;
    }

    .addresses .col-1,
    .addresses .col-2 {
        max-width: 600px;
        width: 100% !important;
        margin: 10px auto !important;
        display: block;
        float: none !important;
    }

    .widget_product_categories ul margin: 10px 0 !important;
    display: block;
}

.widget_product_categories ul li {
    text-align: left;
}

.widget-area .widget a {
    font-weight: 400 !important;
    opacity: 1;
    font-size: .88em;
    padding: 6px 10px;
    color: #000;
    text-align: left;
    text-transform: uppercase;
}

.widget-area .widget a:hover {
    color: #fff;
    background-color: #000;
}

.widget_product_categories ul li:before {
    display: none;
}

.submenu {}

.widget a.botao,
a.reservar {
    display: inline-block;
    width: 100%;
    padding: 8px 16px;
    margin: 0 0 5px 0;
    font-size: 1.16em;
    font-weight: 700 !important;
    color: #fff;
    background: #000;
}

.widget a.botao:hover,
a.reservar:hover {
    background: #eb2944;
    color: #fff;
}

a.reservar:before {
    content: "\ea4c";
}


/* SELECT */

.shipping_calculator {
    float: left !important;
}

.woocommerce-info p {
    color: #fff;
}

.woocommerce-checkout #payment ul.payment_methods p {
    padding: 0.857em 1.387em;
}

.telefone:before,
.atendimento:before {
    font-family: "FontAwesome";
    margin-right: 0.53em;
    font-size: 1em;
    display: inline-block;
    position: relative;
}

.telefone:before {
    content: "\f095";
}

.atendimento:before {
    content: "\f1d8";
}


}

/* ESTRUCTURE */
.row.colunas {}

.colunas .two_col {
    margin: 1%;
    width: 48%;
    display: block;
    float: left;
}

.colunas .three_col {
    margin: 1.5%;
    width: 30%;
    display: block;
    float: left;
}

.colunas strong {
    font-family: 'Titillium Web', sans-serif;
    font-size: 1.465em;
    font-style: normal;
    font-weight: 700;
}

.column,
.columns {
    float: left;
    display: inline;
}

.site-footer .col-2 {
    width: 48%;
    margin: 0 1%;
}

body {
    font-family: 'Titillium Web', sans-serif;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.4;
    color: #000;
}

.stock:before,
a.reset_variations:before,
a.reservar:before,
.woocommerce .woocommerce-ordering:before {
    margin-right: .53em;
    font-family: 'icometal';
}

.stock.in-stock {
    color: #000;
}

.stock.in-stock:before {
    content: "\ea2b";
}

a.reset_variations:before {
    content: "\ea6a";
}

.stock.out-of-stock:before {
    content: "\ea1f";
}

html body .share .supsystic-social-sharing a.social-sharing-button,
html body .share .supsystic-social-sharing.supsystic-social-sharing-spacing a.social-sharing-button {
    font-size: 1.26em;
    margin-right: 0.6em;
}

.single-product div.product .product_meta,
.single-product div.product .woocommerce-product-rating a,
ul.products li.product .star-rating,
.share {
    font-size: 1em;
    margin-top: 30px;
}

ul.products li.product .star-rating {
    font-size: 0.8em;
    margin: -14px auto 0;
    position: absolute;
    left: 0;
    right: 0;
}

.single-product div.product .woocommerce-product-rating {
    margin-bottom: 1em;
    line-height: 1;
    border-bottom: 1px solid rgba(0, 0, 0, .1);
    padding: 0 0 1em;
    margin-top: -.5em;
}

.single-product div.product .product_meta .posted_in,
.single-product div.product .product_meta .sku_wrapper,
.single-product div.product .product_meta .tagged_as,
.share .share-title {
    text-transform: uppercase;
    font-weight: 700;
    border: none;
    padding: 2px 0;
}

.single-product div.product .product_meta .posted_in a,
.single-product div.product .product_meta .sku_wrapper a,
.single-product div.product .product_meta .tagged_as a {
    color: #000;
    text-transform: capitalize;
    font-weight: 400;
}

.site-header .site-branding img,
.site-header .site-logo-anchor img,
.site-header .site-logo-link img {
    max-width: 100px;
}

.site-header .site-logo-link {
    max-width: 100px;
    width: 100px;
    margin-top: 20px;
    margin-right: 2%;
}

.site-header {
    background: transparent;
    margin-top: 30px;
    padding-top: 0 !important;
}

.page-template-template-homepage .site-header {
    margin-top: 0;
}

.page-template-template-homepage .hentry {
    margin-bottom: 0;
    border: none;
}

.page-template-template-homepage .entry-header {
    display: none;
}

.page-template-template-homepage .site-main {
    padding-top: 0 !important;
}

hr {
    margin-top: 35px;
    margin-bottom: 35px;
    background: #000;
    background-size: 200px 4px;
    height: 4px;
}

.home .hentry .wp-post-image,
.page-template-template-homepage .hentry .wp-post-image {
    margin-bottom: 0;
}

h1.product_title.entry-title {
    text-align: left;
    padding-bottom: 0;
    margin-bottom: 15px;
    font-weight: 700;
    font-size: 1.5em;
}

.fixedmenu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #000;
    padding-top: 0;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    z-index: 9999;
}

#site-navigation li,
.site-header-cart li {
    margin-bottom: 0;
    background: #fff;
}

.woocommerce-active .site-header .main-navigation {
    clear: none !important;
    margin-top: 60px;
    margin-bottom: 15px;
    padding: 0;
    width: 70%;
    position: relative;
    z-index: 91;
}

.main-navigation ul.menu>li:first-child,
.main-navigation ul.nav-menu>li:first-child {
    margin-left: 0;
}

.main-navigation ul.menu>li,
.main-navigation ul.nav-menu>li {
    margin: 0;
    padding: 0;
}

.main-navigation ul.menu>li>a,
.main-navigation ul.nav-menu>li>a {
    color: #000;
    text-transform: uppercase;
    font-family: 'Titillium Web', sans-serif;
    font-size: 1.1em;
    font-style: normal;
    font-weight: 700;
    line-height: 1.3em;
    padding: 2px 6px;
    margin: 0;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.main-navigation ul.nav-menu li a:before {
    content: "";
    display: block;
    width: 0;
    height: 3px;
    background-color: rgba(255, 0, 0, 0);
    position: absolute;
    bottom: 0px;
    left: 0;
    -moz-transition: all .3s;
    -webkit-transition: all .3s;
    -o-transition: all .3s;
    transition: all .3s;
}

.main-navigation ul.nav-menu li a:hover:before {
    background-color: rgba(255, 0, 0, 1);
    width: 100%;
}

.main-navigation ul.menu li ul.sub-menu a {
    border-bottom: none;
    font-weight: 500;
    text-transform: uppercase;
}

/* TRASH /////////////// /*/
.page-template-page-lps-usados li.menu-item-7800 a:before {
    color: #000 !important;
    background-color: rgba(255, 0, 0, 1) !important;
    width: 100% !important;
    bottom: 10px !important;
}

ul.menu li.current-menu-item>a {
    color: #000 !important;
}

ul.menu li.current-menu-item>a:before {
    color: #000 !important;
    background-color: rgba(255, 0, 0, 1);
    width: 100%;
    bottom: 10px
}

.main-navigation ul.menu li a:hover {
    color: #000;
}

.main-navigation ul.menu li ul.sub-menu li a:hover {
    color: #333;
}

.main-navigation ul.menu li ul.sub-menu {
    border-left: 2px solid #eb2944;
    background-color: #F9F9F9;
}

.woocommerce-active .site-header .secondary-navigation {
    padding: 0 5% !important;
    margin: 0;
    width: 100% !important;
    text-align: center;
}

.menu-institucional-container,
.secondary-navigation .menu {
    width: 100%;
    display: inline-block;
    text-align: center;
}

#fixedmenu .main-navigation {
    padding-top: 10px;
    width: 100%;
}

.site-header {
    margin-top: 30px;
}

.secondary-navigation .menu>li>a:before {
    display: none;
}

.secondary-navigation .menu {
    text-align: right;
    float: left;
}

.secondary-navigation ul li {
    position: relative;
    display: inline-block;
    text-align: center;
    margin: 0;
}

.secondary-navigation ul.menu>li>a,
.secondary-navigation ul.nav-menu>li>a {
    padding: 8px 10px;
    font-family: 'Titillium Web', sans-serif;
    font-size: 0.88em;
    text-transform: uppercase;
    letter-spacing: -0.02em;
    font-style: normal;
    font-weight: 400;
    line-height: 1.4em;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    color: #fff;
}

.secondary-navigation ul.menu>li>a:hover,
.secondary-navigation ul.nav-menu>li>a:hover {
    color: #fff;
    background-color: #eb2944;
}

.secondary-navigation ul.menu li.current-menu-item>a {
    color: #121212;
    background: #f9f9f9;
}

.wpmenucartli .wpmenucart-contents {
    background-color: #eb2944;
}

.secondary-navigation ul.menu li.logout a {
    background-color: #eb2944;
    color: #fff;
}

.secondary-navigation ul.menu li.logout a:hover {
    background-color: #c0253a;
}

.site-footer .wpmenucartli {
    display: none;
}

.site-footer {
    background-color: #fff;
    color: #000;
    padding: 1.618em 0;
}

.site-footer h1,
.site-footer h2,
.site-footer h3,
.site-footer h4,
.site-footer h5,
.site-footer h6 {
    color: #000;
}

.site-footer a:not(.button) {
    color: #ffffff;
}

.site-header .site-branding,
.site-header .site-logo-anchor,
.site-header .site-logo-link {
    margin-bottom: 0;
    position: absolute;
}

#customer_login {
    width: 100% !important;
    margin: 0 !important;
}

#customer_login h2 {
    font-size: 2em;
}

.woocommerce form.login,
.woocommerce form.checkout_coupon,
.woocommerce form.register {
    border: none !important;
}

.minha-conta {
    padding-top: 38px;
}

.minha-conta .widget [class^="icon-"],
[class*=" icon-"] {
    margin: 0 10px 0 0;
}

h1.nome {
    font-size: 1.8em;
}

.site-footer ul li {
    margin-bottom: 0 !important;
}

.site-footer ul.menu li a {
    padding: 3px 0;
    display: inline-block;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.site-footer li.current-menu-item>a,
.site-footer li a:hover {
    color: #1ecaff !important;
}

.site-info {
    padding: 50px 0 80px;
    border-top: 1px solid #000;
}

.site-footer a:not(.button) {
    font-weight: 300 !important;
}

.site-footer .widget_nav_menu ul li:before {
    color: #fff;
}

.site-footer h4 {
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: 0;
}

.site-footer h3,
.widget .widget-title {
    padding: 10px 0 5px 0;
    margin: 0;
    text-transform: uppercase;
    font-size: 1.1em;
    font-weight: 700;
    border-bottom: none;
    border-top: 1px solid #000;
}

.site-footer h4+a {
    margin-bottom: 14px;
}

.site-footer hr {
    margin-top: 10px;
    margin-bottom: 10px;
    height: 4px;
}

.footer-widgets {
    border-bottom: none;
    padding: 30px 0;
}

.footer-pagamentos img {
    display: inline-block;
    margin: 1px auto 0;
    padding: 0 5px;
}

.widget {
    margin: 0 0 16px;
}


.widget-area .widget ul,
.widget-area .widget ul li {
    margin: 0;
    list-style: none;
    display: inline-block;
}

.widget-area .widget form {
    display: block;
}

.site-header-cart .widget_shopping_cart .product_list_widget {
    display: block;
}

.assinaturas {
    text-align: right;
    line-height: 0;
}

.assinaturas span {
    display: inline-block;
    margin: 0 3px 0 10px;
    font-size: 20px;
    font-weight: 300;
    top: 22px;
    position: relative;
}

#antidesign {
    display: inline-block;
    background-image: url(design/antidesign.svg);
    background-position: center top;
    background-repeat: no-repeat;
    background-size: 80px 72px;
    height: 36px;
    width: 80px;
    font: 0/0 a;
    text-shadow: none;
    color: transparent;
}

#antidesign:hover,
#ame:hover {
    background-repeat: no-repeat;
    background-position: center bottom;
}

#ame {
    display: inline-block;
    background-image: url(design/ame.png);
    background-position: center top;
    background-repeat: no-repeat;
    background-size: 36px 72px;
    height: 36px;
    width: 36px;
    font: 0/0 a;
    text-shadow: none;
    color: transparent;
}

.woocommerce-error,
.woocommerce-info,
.woocommerce-message,
.woocommerce-noreviews,
p.no-comments {
    background-color: #EB2944;
}

.woocommerce-message .button {
    background-color: transparent !important;
    color: #fff !important;
    height: auto;
}

/* Botão PAGAR BOLETO página pedido recebido */
.woocommerce-order-received .woocommerce-order .woocommerce-message .button {
    background-color: #eb2944 !important;
    color: #fff !important;
    height: auto;
    padding: 10px;
}

.woocommerce-order-received .woocommerce-order .woocommerce-message .button:hover {
    background-color: #000 !important;
}

.related.products .add_to_wishlist_button,
.archive .add_to_wishlist_button,
.archive .view_wishlist_button,
.related.products .view_wishlist_button,
.up-sells.products .add_to_wishlist_button {
    display: none;
}

.related.products h2,
.upsells.products h2 {
    margin: 10px 0;
    font-weight: 700;
    font-size: 1.4em;
    text-align: center;
    text-transform: uppercase;
}

/* PRODUTO EXIBIDO LOOP */
ul.products {
    position: relative;
}

.produto-row {
    display: block;
    margin-bottom: 65px;
}

.prod-column {
    width: 65%;
    margin-right: 5%;
    padding-right: 5%;
    border-right: 1px solid #eaeaea;
    display: block;
    float: left;
    text-align: left;
    margin-bottom: 20px;
}

.prod-column h3 {
    margin: 0;
    font-size: 1.1em;
    line-height: 1.1em;
}

.price-column {
    display: block;
    width: 30%;
    text-align: right;
    float: left;
}

.price .row {
    display: block;
}

.price .amount {
    display: inline-block;
    font-size: 1.5em;
    line-height: 0.8em;
    margin: 2px;
    font-weight: 700;
}

.price .amount b {
    font-weight: 300;
    font-size: 0.8em;
}

.price del .amount {
    font-size: 0.9em;
    text-decoration: line-through;
    color: #A09BA1;
    display: inline-block;
    font-weight: 300;
}

.price ins {
    margin: 0 !important;
}

.price ins .amount {
    color: #eb2944;
}

.price del .noline {
    text-decoration: none !important;
    font-size: 0.9em;
    text-decoration: line-through;
    color: #A09BA1;
    display: inline-block;
    font-weight: 300;
}

del .pro_price_area span.p_price {
    text-decoration: line-through;
    font-size: 0.78em;
    color: #b7b7b7;
    margin: 0 5px;
}

/* INFINITE SCROLL */
#infscr-loading {
    display: block;
    width: 100%;
    position: absolute;
    bottom: -20px;
    text-align: center;
    left: 0;
}

#infscr-loading img {
    display: block;
    width: 32px;
    height: 32px;
    margin: 0 auto;
}

.woocommerce-pagination {
    display: block;
    width: 100%;
    border: none;
    padding-bottom: 0;
    margin: 0px 0 50px;
    text-align: center;
    color: #000;
    border-bottom: 4px solid #000;
    display: block;
    font-size: 1em;
}

.woocommerce-pagination ul.page-numbers {
    position: relative;
    font-weight: 300;
    text-decoration: none;
    color: #000;
    text-align: center;
    display: inline-block;
    margin: 0;
    padding: 0 10px;
    top: 14px;
    background: #fff;
    text-transform: uppercase;
}

.pagination .page-numbers li .page-numbers,
.woocommerce-pagination .page-numbers li .page-numbers {
    background-color: #fff;
    border: 1px solid #fff;
}

.pagination .page-numbers li a.page-numbers:hover,
.woocommerce-pagination .page-numbers li a.page-numbers:hover {
    background-color: #fff;
    border: 1px solid #000;
}

.pagination .page-numbers li .page-numbers.current,
.woocommerce-pagination .page-numbers li .page-numbers.current {
    font-weight: 700;
    background-color: #000;
    border: 1px solid #000;
}

.abcd {
    margin: 1em 0;
}

.abcd a {
    display: inline-block;
    padding: .2em .5em;
    background-color: rgba(0, 0, 0, .025);
    color: #60646c;
}

.letra-box a {
    font-weight: 500;
}

.letra-box a:hover {
    text-decoration: underline;
}

.letra-box ul {
    -webkit-columns: 200px 5;
    -moz-columns: 200px 5;
    columns: 200px 5;
}

.letra-box ul li {
    list-style: none;
}

.letra-box .letra {
    font-size: 20px;
    font-weight: 700;
    border-top: 1px solid #000;
}

.abcd a:hover {
    background-color: #000;
    color: #fff;
}

/* SLIDER DESTAQUES */
.owl-theme .owl-controls .owl-buttons div {
    height: 26px;
    width: 26px;
    margin: 0 1px 0 0;
}

.grid figcaption {
    background: rgba(0, 0, 0, 0.75);
}

.grid figcaption h3 {
    font-weight: 300;
}

span.p_price {
    font-weight: 700;
}

.owl-theme .owl-controls .owl-page {
    display: inline-block;
    zoom: 1;
    margin: 0 1px 0 0;
    font-weight: 500;
}

/* LISTA SIDEBAR */
.widget_nav_menu ul li:before {
    content: "\f101";
}

.widget_nav_menu ul li ul li:before {
    content: "\f105";
}

.widget_nav_menu ul li ul .current-menu-item:before {
    content: "\f0da";
}

/* LIGHT BOX */
div.pp_woocommerce div.ppt,
.pp_details {
    display: none !important;
}

div.pp_woocommerce .pp_content_container {
    border-radius: 0 !important;
    box-shadow: none !important;
    padding: 0 !important;
}

div.pp_woocommerce .pp_top .pp_middle,
div.pp_woocommerce .pp_content_container .pp_left,
div.pp_woocommerce .pp_content_container .pp_right,
div.pp_woocommerce .pp_bottom .pp_middle,
div.pp_woocommerce .pp_left,
div.pp_woocommerce .pp_middle,
div.pp_woocommerce .pp_right,
div.pp_woocommerce .pp_content,
div.facebook .pp_content {
    background-color: #fff !important;
}

.pp_gallery {
    bottom: 0px !important;
    z-index: 9090;
}

.pp_details {
    display: none !important;
}

.pp_gallery {
    opacity: 1 !important;
    -webkit-opacity: 1 !important;
    -moz-opacity: 1 !important;
    display: block !important;
}

div.pp_woocommerce .pp_gallery ul li a {
    border: 1px rgba(0, 0, 0, 0.15) solid;
    border-radius: 0;
    box-shadow: none !important;
}

.catablog-navigation-link.catablog-disabled {
    display: none;
}

.thumbs {
    display: none;
}

div.pp_woocommerce .pp_next,
div.pp_woocommerce .pp_previous {
    opacity: 0.5 !important;
    -webkit-opacity: 0.5 !important;
}

div.pp_woocommerce .pp_next:hover,
div.pp_woocommerce .pp_previous:hover {
    opacity: 1 !important;
    -webkit-opacity: 1 !important;
}

div.pp_woocommerce .pp_next:before,
div.pp_woocommerce .pp_previous:before,
div.pp_woocommerce .pp_arrow_next:before,
div.pp_woocommerce .pp_arrow_previous:before {
    font-family: "FontAwesome" !important;
    display: block;
    border-radius: 0;
    height: 1em;
    width: 1em;
    text-shadow: none;
    background-color: transparent;
    color: #333 !important;
    font-size: 32px !important;
    line-height: 1em;
    transition: all ease-in-out .2s;
    text-indent: 0;
    position: absolute;
    top: 50%;
    margin-top: -10px;
    text-align: center;
}

div.pp_woocommerce .pp_arrow_next,
div.pp_woocommerce .pp_arrow_previous {
    font-size: 20px !important;
    border-radius: 0;
    text-shadow: none;
    background-color: transparent;
    color: #333 !important;
    opacity: 0.5 !important;
    -webkit-opacity: 0.5 !important;
}

div.pp_woocommerce .pp_next:before,
div.pp_woocommerce .pp_arrow_next:before {
    content: "\f105";
    right: 0;
}

div.pp_woocommerce .pp_previous:before,
div.pp_woocommerce .pp_arrow_previous:before {
    content: "\f104";
    left: 0;
}

div.pp_woocommerce a.pp_contract,
div.pp_woocommerce a.pp_expand {
    top: -10px;
    left: 10px;
    border-radius: 0;
    text-shadow: none;
    background-color: transparent;
    color: #333 !important;
    opacity: 0.5 !important;
    -webkit-opacity: 0.5 !important;
}

div.pp_woocommerce a.pp_contract:hover,
div.pp_woocommerce a.pp_expand:hover {
    text-shadow: none;
    background-color: transparent;
    opacity: 1 !important;
    -webkit-opacity: 1 !important;
}

div.pp_woocommerce a.pp_contract:before,
div.pp_woocommerce a.pp_expand:before {
    font-family: "FontAwesome" !important;
    display: block;
    font-weight: 300;
}

div.pp_woocommerce a.pp_expand:before {
    content: "\f00e";
}

div.pp_woocommerce a.pp_contract:before {
    content: "\f010";
}

/* CART */
.woocommerce-cart .cart-collaterals .cart_totals tr th {
    width: 35%;
}

.woocommerce-cart .cart-collaterals .cart_totals tr td {
    width: 65%;
}

.single-product div.product form.cart {
    margin-top: 1.618em;
}

/* PRODUTO */
.woocommerce span.onsale {
    position: absolute;
    top: .618em;
    right: 1px;
    padding: .53em .857em;
    background: rgba(0, 0, 0, 0.5) !important;
    color: #fff !important;
    font-weight: 700;
    box-shadow: 5px 0 0 0 rgba(0, 0, 0, 0.5);
    font-family: 'Titillium Web', sans-serif;
    border: none;
    z-index: 2;
}

.woocommerce span.onsale:after {
    content: "";
    display: block;
    position: absolute;
    right: -5px;
    top: 100%;
    border: 5px solid transparent;
    border-top-color: #000000;
    border-left-width: 0;
}

.page.home .site-content,
.page.page-template-template-homepage .site-content {
    margin-top: 0;
}

.single-product span.onsale {
    right: 0;
    box-shadow: none;
    position: relative;
}

.hentry {
    border-bottom: none;
}

.hentry .entry-header {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #000;
}

.hentry .entry-header h1 {
    border-bottom: none;
    color: #000;
    margin: 0;
    padding: 0;
    font-size: 2.4em;
    font-weight: 700;
    line-height: 1.36em;
}

.single-product span.onsale:after {
    border: none;
}

ul.products li.product {
    overflow: visible !important;
}

.single-product div.product .images .woocommerce-main-image {
    margin-bottom: 12px !important;
    text-align: center;
}

.single-product div.product .images .thumbnails,
.yith_magnifier_gallery {
    text-align: center;
    display: inline-block;
    width: 100%;
}

.yith_magnifier_gallery li {
    position: relative;
    float: none;
    display: inline-block;
    width: 30%;
}

.single-product div.product .images .thumbnails a.zoom {
    width: 22% !important;
    float: none !important;
    text-align: center !important;
    background-color: #fff;
    display: inline-block !important;
    margin: 4px !important;
    border: 1px solid #fff;
}

.flex-control-nav.flex-control-thumbs {
    width: 100% !important;
    text-align: center !important;
    margin: 20px auto 0;
    list-style: none;
}

.flex-control-nav.flex-control-thumbs li {
    width: auto;
    max-width: 120px;
    position: relative;
    float: none;
    margin: 0 10px;
    display: inline-block;
    cursor: pointer;
    padding: 5px;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

.flex-control-nav.flex-control-thumbs li:hover {
    box-shadow: 0 0 0 2px #000;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"],
.button,
.added_to_cart,
button.cta,
button.alt,
input[type="button"].cta,
input[type="button"].alt,
input[type="reset"].cta,
input[type="reset"].alt,
input[type="submit"].cta,
input[type="submit"].alt,
.button.cta,
.button.alt,
.added_to_cart.cta,
.added_to_cart.alt,
ul.products li.product .button,
.add_to_wishlist_button.button,
.site-header-cart .widget_shopping_cart a.button,
.widget_price_filter .price_slider_amount .button {
    box-shadow: none;
    border: none;
    height: auto;
    line-height: 1.5em;
    background: #b21f33;
    color: #FFFFFF !important;
    position: relative;
    padding: 8px 20px;
    text-transform: uppercase;
    -webkit-transition: all 0.2s ease-in-out;
    -moz-transition: all 0.2s ease-in-out;
    -ms-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

ul.products li.product .button:hover,
button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover,
.button:hover,
.added_to_cart:hover,
.widget-area .widget a.button:hover,
button.cta:hover,
button.alt:hover,
input[type="button"].cta:hover,
input[type="button"].alt:hover,
input[type="reset"].cta:hover,
input[type="reset"].alt:hover,
input[type="submit"].cta:hover,
input[type="submit"].alt:hover,
.button.cta:hover,
.button.alt:hover,
.added_to_cart.cta:hover,
.added_to_cart.alt:hover,
ul.products li.product .button:hover,
.add_to_wishlist_button.button:hover,
.site-header-cart .widget_shopping_cart a.button:hover,
.widget_price_filter .price_slider_amount .button:hover {
    color: #FFFFFF !important;
    opacity: 1;
    background: #eb2944;
}

.single_add_to_cart_button.button:disabled:before {
    content: "ESCOLHA SEU TAMANHO PARA";
    display: block;
    position: absolute;
    width: 100%;
    height: 25px;
    left: 0;
    top: -25px;
    font-weight: 400;
    font-size: 0.78rem;
    background-color: #000;
    line-height: 25px;
    color: #fff;
}

.woocommerce-tabs ul.tabs:before,
.woocommerce-tabs ul.tabs li:after,
.woocommerce-tabs ul.tabs li:before,
.woocommerce-tabs ul.tabs li a:after,
.woocommerce-tabs ul.tabs li a:before {
    display: none !important;
}

.woocommerce-tabs ul.tabs li.active:after {
    margin: -.5em 0 0 !important;
    right: 0 !important;
    left: auto !important;
    top: 50% !important;
    content: "\f105" !important;
    display: block !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    border-radius: 0 !important;
    border: none !important;
}

.woocommerce-tabs ul.tabs,
.woocommerce-tabs ul.tabs li,
.woocommerce-tabs ul.tabs li a {
    background: transparent !important;
    box-shadow: none;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    border-radius: 0 !important;
}

/* SIMPLE TAB CSS */
.woocommerce-tabs ul.tabs li {
    display: block !important;
    margin: 0 !important;
    border-bottom: 1px dotted rgba(0, 0, 0, .1) !important;
}

.woocommerce-tabs ul.tabs li a {
    padding: 10px 10px !important;
    display: block;
}

.woocommerce-tabs ul.tabs li.active {
    background-color: transparent !important;
}

.pro_price_area {
    display: inline-block;
}

.from,
.to {
    font-size: .88em;
    font-weight: 400;
    line-height: 1em;
    color: #000;
    text-decoration: none;
}

.wpb_slider_area .from,
.wpb_slider_area .to {
    font-size: 12px;
    font-weight: 300;
    color: #fff;
}

ul.gform_fields {
    margin: 0;
    padding: 0;
}

ul.gform_fields .gfield {
    overflow: hidden;
    margin-bottom: 25px;
}

.gform_fields label {
    color: #454545;
}

.woocommerce select {
    position: relative;
    display: inline-block;
    margin: 0;
    padding: 4px;
    line-height: 1.5;
    color: #121212;
    cursor: pointer;
    outline: 0;
}

.woocommerce .shipping-calculator-form select {
    color: #444;
    line-height: 18px;
    font-weight: 500;
    display: block;
    padding-left: 8px;
    padding-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
    text-transform: capitalize;
}

.woocommerce select option {
    color: #7d7d7d;
    font-size: 12px;
    padding: 3px;
    border: 0px;
}

/* Undo the Firefox inner focus ring */
.woocommerce select:focus:-moz-focusring {
    color: transparent;
    text-shadow: 0 0 0 #454545;
}

/* Dropdown arrow */
.woocommerce select:after {
    position: absolute;
    top: 50%;
    right: 1.25rem;
    display: inline-block;
    content: "v";
    width: 0;
    height: 0;
    margin-top: -.15rem;
    pointer-events: none;
    border-top: .35rem solid;
    border-right: .35rem solid transparent;
    border-bottom: .35rem solid transparent;
    border-left: .35rem solid transparent;
}

/* Hover state */

/* Uncomment if you need it, but be aware of the sticky iOS states.
.select select:hover {
  background-color: #ddd;
}
*/

/* Focus */
.woocommerce select select:focus {}

/* Active/open */
.woocommerce select select:active {
    color: #fff;
    background-color: #0074d9;
}

/* Hide the arrow in IE10 and up */
.woocommerce select select::-ms-expand {
    display: none;
}


/*------------- Css novo --------------*/

#customer_details .col-1,
#customer_details .col-2 {
    max-width: 750px;
}

/*
#customer_details .col-2 {
    border: 1px solid #000;
}
*/

.col-full:after,
.col-full:before {
    content: inherit;
}

header .col-full {
    max-width: 100%;
    margin: 0;
    position: relative;
    padding: 0;
}

.main-navigation ul.menu,
.main-navigation ul.nav-menu {
    margin-left: 0;
}

.header-full {
    margin-right: 0;
    display: inline-block;
    width: 100%;
    margin-top: 0;
}

.site-header-cart .cart-contents .amount {
    font-weight: 700;
}

.col-full {
    max-width: 1442px;
    margin: 0;
    padding-left: 0px;
}

.site-search .widget_product_search form input[type=search] {
    padding-left: 45px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700;
}

h2,
.beta {
    letter-spacing: -1px;
}

.page-template-bandas .storefront-breadcrumb,
.site .storefront-breadcrumb {
    display: none;
}

.site-content .storefront-breadcrumb {
    display: block;
    padding: 0;
    margin: 0;
}

.widget-area .widget a:not(.button) {
    text-decoration: inherit;
}

a {
    text-decoration: none;
    font-weight: 700;
}

.woocommerce-pagination .page-numbers li .page-numbers.current {
    color: #fff;
}

ul.products li.product .price {
    color: #60646c;
}

.woocommerce-pagination .next,
.woocommerce-pagination .prev {
    text-indent: inherit;
}

.woocommerce-pagination .next:after,
.woocommerce-pagination .prev:after,
.woocommerce-breadcrumb a:first-of-type:before {
    content: "";
    margin-right: 0;
}

.woocommerce-pagination .page-numbers li .page-numbers {
    color: #60646c;
}

.storefront-breadcrumb .col-full {
    padding-right: 0;
}

#brand {
    top: 32px;
}

.menu-institucional-container {
    display: block;
}

.widget_layered_nav ul li {
    padding-left: 0;
}

.widget-area .widget.widget_layered_nav ul li a {
    font-weight: 700 !important;
}

.price del {
    opacity: 1;
}

.onsale {
    border-radius: 0;
}

.single-product div.product .woocommerce-product-gallery .flex-control-thumbs {
    margin: 20px auto 0;
}

.single-product div.product .woocommerce-product-gallery.woocommerce-product-gallery--columns-4 .flex-control-thumbs li {
    width: auto;
    margin-right: 0;
    float: inherit;
    margin: 0 10px;
}

.single-product div.product .product_meta a {
    text-decoration: inherit;
}

.single-product div.product p.price {
    font-size: 1em;
}

label {
    font-weight: 700;
}

.woocommerce-tabs ul.tabs li.active a {
    color: #60646c;
}

.woocommerce-tabs ul.tabs li.active:after {
    margin: 0 !important;
}

.stock:before {
    font-weight: 500;
}


.storefront-product-pagination a {
    background: #fff;
}

table.cart th {
    text-transform: uppercase;
}

table.cart td.actions {
    border-top: 0;
}

a.remove::before {
    font-size: 15px;
}

.hentry.type-page .entry-header {
    border-bottom: 1px solid #000;
}

input[type=checkbox]+label,
input[type=radio]+label,
ul#shipping_method input {
    margin: 0;
}

#payment .payment_methods>.wc_payment_method>label,
#payment .payment_methods>.woocommerce-PaymentMethod>label {
    /* display: block; */
    padding: 0;
    line-height: 1 !important;
    display: flex;
}

#payment .payment_methods li.wc_payment_method>input[type=radio]:first-child {
    width: auto;
    height: auto;
    position: relative;
    clip-path: inherit;
}

#payment .payment_methods li {
    list-style: none !important;
    padding: 1.387em !important;
    border-bottom: 1px dotted rgba(0, 0, 0, .1);
    margin-left: 0;
}

#payment .payment_methods li .payment_box {
    padding: 1.387em;
    margin: 1.387em -1.387em -1.387em;
    background: rgba(0, 0, 0, .035);
    border-top: 1px dotted rgba(0, 0, 0, .1);
}

#payment .payment_methods>.woocommerce-PaymentMethod>label::before,
#payment .payment_methods>.wc_payment_method>label::before,
#payment .payment_methods li.woocommerce-PaymentMethod>input[type=radio]:first-child:checked+label::before,
#payment .payment_methods li.wc_payment_method>input[type=radio]:first-child:checked+label::before {
    content: "";
}

ul.order_details {
    display: flex;
    display: -webkit-flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    border: 1px solid rgba(0, 0, 0, .1);
}

ul.order_details li:first-child {
    padding-top: 1em;
}

ul.order_details li {
    float: left;
    padding: 1em 1.618em;
    border-right: 1px solid rgba(0, 0, 0, .1);
    font-size: .8em;
    text-transform: uppercase;
}

.hentry.type-page .entry-header {
    margin-bottom: 10px;
}

table thead th {
    text-transform: uppercase;
}

.col2-set.addresses {
    display: flex;
    display: -webkit-flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
}

.addresses .woocommerce-column {
    width: 50% !important;
    max-width: 50%;
    padding: 1em 1.41575em;
    margin: 0 !important;
}

.left-sidebar:not(.page-template-template-fullwidth-php) .woocommerce-MyAccount-content {
    width: 100%;
    margin-right: 0;
}

table.my_account_orders {
    font-size: 14px;
}

fieldset {
    padding: 0;
}

.input-text,
input[type=email],
input[type=password],
input[type=search],
input[type=tel],
input[type=text],
input[type=url],
textarea {
    border: 1px solid #b3b3b3;
}

.woocommerce select {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
    font-size: 1em;
    font-weight: 400;
    line-height: 1em;
    text-transform: capitalize;
}

.woocommerce select:after {
    content: inherit;
}

.woocommerce-wishlist-wrapper .stock.in-stock:after {
    content: "\ea2b";
    margin-right: .53em;
    font-family: 'icometal';
}

.contato h1 {
    font-weight: 300;
    font-size: 34px;
}

blockquote+h2,
blockquote+h3,
blockquote+h4,
blockquote+header h2,
form+h2,
form+h3,
form+h4,
form+header h2,
ol+h2,
ol+h3,
ol+h4,
ol+header h2,
p+h2,
p+h3,
p+h4,
p+header h2,
table+h2,
table+h3,
table+h4,
table+header h2,
ul+h2,
ul+h3,
ul+h4,
ul+header h2 {
    margin-top: 0;
}

.storefront-full-width-content .woocommerce-products-header,
.storefront-full-width-content.woocommerce-account .entry-header,
.storefront-full-width-content.woocommerce-cart .entry-header,
.storefront-full-width-content.woocommerce-checkout .entry-header {
    text-align: left;
    padding: 0 0 20px;
}

.steps-set .col2-set.addresses .col-2.woocommerce-Address {
    border: 0;
}

/*.single-product div.product .woocommerce-product-gallery__image img,*/
.woocommerce-product-gallery__wrapper {
    width: 100%;
    /*    height: 578px;*/
    height: auto;
    margin-bottom: 20px;
    object-fit: cover;
}

.woocommerce-product-gallery__image {
    height: 100%;
}

.single-product div.product .woocommerce-product-gallery__image img {
    width: 100%;
}

.page-template-template-homepage-php #brand {
    top: 0;
}

.single-product div.product .woocommerce-product-gallery .woocommerce-product-gallery__trigger {
    background-color: transparent;
}

.woocommerce-checkout .site-footer {
    padding-left: 160px;
}

div#cfpp {
    margin: 60px 0 0 0;
}

.single-product div.product .product_meta {
    margin-top: 15px;
}

div#cfpp div.calculo-de-frete div#calcular-frete svg {
    display: none;
}

div#cfpp .calculo-de-frete div#calcular-frete {
    padding: 0 20px 0 20px;
}

div#cfpp div.calculo-de-frete div#calcular-frete {
    background-color: #666 !important;
    margin-left: 20px;
    position: relative;
    top: -3px;
}

#calcular-frete-loader {
    border: 4px solid #b21f33;
    border-top: 4px solid #eb2944;
    position: relative;
    top: -9px;
    left: 20px;
    width: 25px;
    height: 25px;
}

div#cfpp .calculo-de-frete div#calcular-frete {
    height: 42px;
    line-height: 40px;
}

div#cfpp .calculo-de-frete input {
    height: 42px;
}

a#cfpp_credits {
    display: none;
}

.resultado-frete thead td {
    font-weight: bold;
}

footer .footer_redes-sociais .brand {
    display: none !important;
}

.footer_redes-sociais {
    display: none !important;
}

@media screen and (max-width: 767px) {
    a:focus {
        outline: none;
    }

    .footer_redes-sociais {
        display: block !important;
    }

    .symbol {
        display: inline-block;
        margin: 22px 12px 29px;
        font-size: .55em;
        float: left;
        text-align: center;
    }

    .symbol:before {
        background-color: #fff;
        color: #000;
    }

    #brand {
        position: absolute;
        width: 200px;
        left: 10px;
        margin-left: 0;
        height: 90px;
        z-index: 9090;
        background: transparent;
    }


    footer .footer_redes-sociais #brand {
        position: relative;
        width: 100%;
        left: 0;
        height: 90px;
        z-index: inherit;
        background: none;
        display: flex;
        justify-content: center;
    }


    footer .footer_redes-sociais .symbol {
        font-size: 1.5em;
    }

    #brand img {
        height: 84px;
        margin: 0;
    }

    #brand .brand {
        margin: 0;
        width: auto;
    }

    .single-product .site-content {
        margin-top: 10px;
    }

    #brand .inner {
        padding: 0;
    }

    #page {
        padding-left: 0;
    }

    .col-full,
    .header-full,
    .left-sidebar .content-area,
    .left-sidebar .widget-area,
    .excursoes-lista li .poster,
    .excursoes-lista li .info,
    .column-content,
    .column-broken,
    .column-lista,
    .left-sidebar .coluna-esquerda,
    .left-sidebar .conteudo-direito {
        max-width: none;
        width: 100%;
        margin: 0;
        position: relative;
    }

    .content-fullwidth .entry-content.page-banner {
        padding-left: 0;
    }

    .site-header {
        background: #fff;
        margin-top: 0;
    }

    .menu-toggle {
        margin: 0 auto;
        width: 100%;
        padding: 5%;
        height: 90px;
        text-indent: -9999px;
        background: #000;
    }

    .menu-toggle:before {
        font-size: 1.8em;
        text-indent: 1px;
        top: 35px;
        right: 20px;
        margin: 0;
        left: auto;
        position: absolute;
        color: #fff;
        display: block;
    }

    .widget,
    .entry-content {
        margin: 0 5%;
    }

    .single-product div.product .images,
    .single-product div.product .summary {
        margin-bottom: 30px;
    }

    .woocommerce-breadcrumb,
    ul.products {
        margin-bottom: 0;
        text-align: center;
        margin: 0 5%;
    }

    ul.products,
    .single-product div.product .images,
    .single-product div.product .summary {
        margin: 0 5% 30px;
    }

    ul.products,
    .single-product div.product .images {
        max-width: 400px;
        height: auto;
        margin: 0 auto;
    }

    .woocommerce-active .site-header .site-search {
        background: transparent;
        background-size: 100%;
        position: relative;
        width: auto;
        height: auto;
        top: auto;
        right: auto;
        margin: 0;
        z-index: 90;
    }

    .page-title {
        text-align: center;
        text-transform: capitalize;
        font-weight: 700;
        font-size: 2em;
    }

    .storefront-sorting {
        text-align: center;
    }

    .woocommerce .woocommerce-ordering {
        display: inline-block;
        margin: 0;
    }

    .woocommerce .woocommerce-ordering:before {
        top: 3px;
    }

    .woocommerce-active .site-header .main-navigation {
        margin: 0;
        padding: 0;
        width: 100%;
        position: relative;
        z-index: 91;
    }

    .site-search form {
        margin: 0;
    }

    .banner h1 {
        font-size: 1.6em;
    }

    .banner h1 span {
        display: inline-block;
        position: relative;
        line-height: 1em;
        text-align: left;
        width: 100%;
        top: 0;
        padding: 10px 5%;
        background-color: #eb2944;
    }

    .slick-dots {
        display: none;
        width: 0;
        height: 0;
        visibility: hidden;
    }

    .banner h2 {
        font-size: 1em;
        font-weight: 400;
        text-align: left;
        margin: 0;
        width: 100%;
        line-height: 1.2em;
        float: right;
        padding: 10px 5%;
    }

    #fixedmenu {
        display: none;
    }

    .woocommerce-active .site-header .site-header-cart {
        top: 26px;
        right: 60px;
    }

    .site-header-cart .cart-contents:after {
        padding: 0;
        line-height: 1.6em;
        font-size: 1.6em;
    }

    .site-header-cart .cart-contents {
        height: 40px;
        width: 40px;
        padding: 0;
        background: #000;
    }

    .widget.woocommerce.widget_product_search {
        top: 0;
        width: 100%;
        left: auto;
    }

    .site-search .widget_product_search input[type=search],
    .site-search .widget_product_search input[type=text] {
        padding: 10px 10px 10px 40px;
        color: #000;
        font-weight: 700;
        border: none;
    }

    .site-search .widget_product_search form:before {
        top: 14px !important;
        left: 10px !important;
        color: #000;
    }

    .main-navigation ul li a:before {
        display: none;
    }

    .page-banner,
    .content-fullwidth .entry-content {
        padding-left: 0;
    }

    .banner:before {
        right: auto;
        left: 0;
        background: url(design/banner-crop.png) left top no-repeat;
        background-size: 100%;
    }

    .banner img {
        padding-left: 0;
    }

    .banner.home h1 {
        font-size: 1.6em;
    }

    .banner.home h2 {
        font-size: 1em;
    }

    .home-novidades {
        margin-top: 0;
    }

    .home-institucional,
    .home-novidades-titulo,
    .novidade {
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .verticalline {
        height: auto;
    }

    .verticalline h2 {
        top: auto;
    }

    .verticalline:after {
        display: none;
    }

    .horarios .inner {
        text-align: center;
    }

    .cadastro.newsletter h4,
    .cadastro.cadastro-excursoes h4 {
        margin-bottom: 20px;
    }

    .cadastro p {
        line-height: 1em;
        margin-top: 20px;
    }

    .cadastro.newsletter input[type="email"],
    .cadastro button,
    .cadastro form {
        width: 80%;
        margin: 0;
    }

    .cadastro input[type="submit"] {
        margin: -15px 0 0 0;
    }

    .page-template-template-homepage-php .site-main .columns-4 ul.products li.product,
    .page-template-template-fullwidth-php .site-main .columns-4 ul.products li.product,
    .page-template-template-homepage-php .site-main .columns-4 ul.products li.product,
    .storefront-full-width-content .site-main .columns-4 ul.products li.product,
    .site-main ul.products li.product {
        margin: 2%;
        width: 46%;
    }

    .site-footer .col-2 {
        width: 100%;
        margin: 0;
    }

    .site-footer,
    .assinaturas {
        text-align: center;
    }

    .page-template-template-fullwidth .site-footer {
        padding-left: 0;
    }

    #antidesign {
        margin: 20px 0;
    }

    .banner .slick-list {
        padding: 0;
    }

    .banner.home .slider-title,
    .banner .slider-title {
        width: 100%;
        bottom: 0;
        position: relative;
    }

    .woocommerce-result-count {
        margin: 0;
    }

    .hentry .entry-header {
        padding-bottom: 10px;
        border-bottom: 1px solid #000;
        margin: 0 5% 10px;
    }

    .main-navigation ul li a {
        border-bottom: 1px solid #000;
    }

    .main-navigation ul li a:hover {
        color: #fff;
        background: #000;
    }

    .progressbar {
        margin: 20px auto;
        padding: 0;
        display: block;
        width: 100%;
        counter-reset: step;
    }

    .steps-set .col2-set.addresses .col-2.woocommerce-Address {
        width: 100%;
        margin: 5px 0;
    }

    .single-product #wc-correios-simulator .cart #zipcode,
    .single-product div.product #wc-correios-simulator form.cart .button {
        width: 100%;
        margin: 0;

    }

    button.menu-toggle,
    button.menu-toggle:hover {
        background: #000;
    }

    button.menu-toggle span::before,
    button.menu-toggle::after,
    button.menu-toggle::before {
        right: 19px;
        left: inherit;
        background: #fff;
        height: 4px;
        width: 28px;
    }

    #brand {
        top: 0
    }

    button.menu-toggle::after {
        margin-top: 7px;
    }

    button.menu-toggle::before {
        top: 35px;
        margin: 0;
        left: auto;
        position: absolute;
        color: #fff;
        display: block;
        margin-top: 1px;
    }

    button.menu-toggle span::before {
        margin-top: 0px;
    }

    .toggled button.menu-toggle::after {
        margin-top: 0;
    }

    .toggled button.menu-toggle::before {
        margin-top: 8px
    }

    .woocommerce-active .site-header .site-search {
        display: block;
        position: absolute;
        width: 100%;
        top: 85px;
    }

    table.cart .product-thumbnail img {
        margin: 0;
    }

    table.cart td.product-remove a.remove {
        top: 0;
    }

    .site-header-cart .cart-contents {
        display: block;
        border: 0;
    }

    .site-header-cart .cart-contents:after {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        display: inline-block;
        font-style: normal;
        font-variant: normal;
        font-weight: 400;
        line-height: 1;
        vertical-align: -.125em;
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        line-height: inherit;
        vertical-align: baseline;
        content: "\f291";
        height: 1em;
        float: right;
        line-height: 1.618;
    }

    .site-header-cart li {
        background: transparent;
    }

    .woocommerce-active .site-header .site-header-cart {
        width: auto;
        top: 28px;
        right: 65px;
    }

    .main-navigation ul.menu li a:hover {
        color: #fff;
    }

    ul.products {
        max-width: 100%;
    }

    .site-header-cart .cart-contents .amount,
    ..site-header-cart .count {
        display: none;
    }
}

@media screen and (max-width: 568px) {
    .page-template-template-fullwidth-php .addresses .woocommerce-column {
        width: 100% !important;
        max-width: 100%;
    }

    .whatsapp-flutuante {
        right: 10px !important;
        bottom: 20px !important;
        width: 40px !important;
        height: 40px !important;
    }

    #brand {
        width: 70px;
    }

    .site-header .site-branding {
        margin-left: 80px;
    }

    .cadastro.newsletter input[type="email"],
    .cadastro button,
    .cadastro form {
        width: 73%;
        margin: 0;
    }

    .cadastro input[type="submit"] {
        margin: -15px 0 0 0;
    }

}

@media screen and (max-width: 425px) {

    .site-branding .site-title a {
        font-size: 27px;
    }

    .cadastro.newsletter input[type="email"],
    .cadastro button,
    .cadastro form {
        width: 66%;
        margin: 0;
    }

    .cadastro input[type="submit"] {
        margin: -15px 0 0 0;
    }

}

@media screen and (max-width: 375px) {

    .site-branding .site-title a {
        font-size: 22px;
    }

    .site-branding .site-description {
        width: 65%;
        font-size: 12px;
    }

    .cadastro.newsletter input[type="email"],
    .cadastro button,
    .cadastro form {
        width: 50%;
        margin: 0;
    }

    .cadastro input[type="submit"] {
        margin: -15px 0 0 0;
    }

}

@media screen and (max-width: 320px) {

    .site-branding .site-title a {
        font-size: 15px;
    }

    .site-branding .site-description {
        display: none !important;
    }

}

/* Media query to target Firefox only */
@-moz-document url-prefix() {
    /* Firefox hack to hide the arrow */

    .woocommerce select {
        text-indent: 0.01px;
        text-overflow: '';
        padding-right: 1rem;
    }

    /* elements inherit styles from, so reset them. */

    .woocommerce select option {
        background-color: white;
    }
}

/* IE9 hack to hide the arrow */
@media screen and (min-width:0\0) {
    .woocommerce select select {
        z-index: 1;
        padding: .5rem 1.5rem .5rem 1rem;
    }

    .woocommerce select:after {
        z-index: 5;
    }

    .woocommerce select:before {
        position: absolute;
        top: 0;
        right: 1rem;
        bottom: 0;
        z-index: 2;
        content: "";
        display: block;
        width: 1.5rem;
        background-color: #eee;
    }

    .woocommerce select select:hover,
    .woocommerce select select:focus,
    .woocommerce select select:active {
        color: #555;
        background-color: #eee;
    }
}

.site-search .woocommerce-product-search .screen-reader-text {
    display: none;
}

.coupon-message {
    text-align: left;
}

#payment .payment_methods li label {
    width: 93%;
    padding-top: 0px;
    /*margin-left: .618em;*/
    font-weight: 700;
    float: right;
    cursor: pointer;
    display: inline-block;
}

#payment .payment_methods li img.an_logo-gateway {
    float: none;
    display: inline-block;
    cursor: pointer;
}

#wc-moip-payment-checkout-form .woocommerce-tabs {
    padding: 1em 0;
}


#wc-moip-payment-checkout-form #payment .payment_methods {
    border-bottom: 0;
}

#wc-moip-payment-checkout-form #payment .payment_methods li {
    padding: 0 !important;
    border-bottom: 0;
    margin-top: 1em;

}

#wc-moip-payment-checkout-form #payment .payment_methods li .payment_box {
    margin: 1.387em 0 0 0;
}

/* Box do paypal fica pequeno, aqui forçamos ficar com altura maior */
.woocommerce-checkout .woocommerce-checkout-payment .wc_payment_methods .payment_method_wc-ppp-brasil-gateway #wc-ppb-brasil-wrappers #wc-ppp-brasil-container,
.woocommerce-checkout .woocommerce-checkout-payment .wc_payment_methods .payment_method_wc-ppp-brasil-gateway #wc-ppb-brasil-wrappers #wc-ppp-brasil-container iframe {
    height: 480px !important
}

/* Não apresenta o cupom na página Finalizar Compra (checkout) */
.woocommerce-checkout .woocommerce-form-coupon-toggle {
    display: none;
}


/* ALTERAR COR DA TELA DE PRODUTO RECEBIDO */

body.woocommerce-order-received .woocommerce-message {
    background-color: #fff;
    color: #000;
    border-left: 0;
}

.woocommerce-wishlist-wrapper table.cart th {
    text-indent: initial;
}

.woocommerce-wishlist-wrapper table.cart .product-thumbnail {
    display: table-cell;
}

.site-header-cart .cart-contents::after {
    box-sizing: unset;
}

/* Botão PAGAR BOLETO página pedido recebido */
.woocommerce-order-received .woocommerce-order .woocommerce-message .button {
    float: left;
    margin: 7px 18px 10px 0;
}

.payment_method_woo-moip-official img.an_logo-gateway {
    width: auto;
}

.order_details a,
.order_details a:hover,
.order_details a:active,
.order_details a:visited {
    color: #000;
}

.whatsapp-flutuante {
    position: fixed;
    width: 60px;
    height: 60px;
    bottom: 40px;
    right: 40px;
    border-radius: 50px;
    box-shadow: 2px 2px 3px #999;
    cursor: pointer;
    z-index: 2;
}

span.xwh-heading,
.xwh-subheading {
    text-align: left !important;
}

.xoo-wl-notloggedin-cont {
    margin: 0 !important;
}

.xoo-aff-input-icon {
    display: none !important;
}

.xoo-aff-cont-email {
    width: 100% !important;
}

.xoo-wl-form {
    width: 300px;
}

.xoo-aff-email {
    margin-bottom: 15px !important;
}

.xoo-aff-group {
    margin-bottom: 0 !important;
}

.site-branding .site-title a {
    white-space: nowrap;
}

.secondary-navigation ul.menu>li>a,
.secondary-navigation ul.nav-menu>li>a {
    padding: 8px 5px;
}

@media(max-width: 768px) {

    /*
    .site-branding .site-title a{
        display: none;
    }
  */
    .site-branding .site-description {
        width: 165%;
        display: block;
    }

    .symbol {
        display: none;
    }

    footer .footer_redes-sociais .symbol {
        display: block;
    }

    .secondary-navigation ul.menu>li>a,
    .secondary-navigation ul.nav-menu>li>a {
        padding: 8px 3px;
    }

    #payment ul.wc_payment_methods li.payment_method_pagseguro .payment_method_pagseguro p:first-child {
        display: none;
    }

    #payment ul.wc_payment_methods li.payment_method_pagseguro .payment_method_pagseguro p#pagseguro-card-holder-name-field {
        display: block !important;
    }


    #payment ul.wc_payment_methods li.payment_method_pagseguro label[for=payment_method_pagseguro] {
        line-height: 0;
    }

}

/* AVISE-ME QUANDO CHEGAR - PLUGIN */
.xoo-aff-input-icon {
    display: none;
}

.xoo-wl-form {
    width: 100% !important;
    background: black;
    padding: 15px 10px;
}

.xoo-wl-form input,
.xoo-wl-form button {
    border-radius: 0 !important;
}

.xoo-wl-form button.xoo-wl-submit-btn {
    width: 100% !important;
    margin: 0;
    max-width: 100%;
    background: #b21f33 !important;
}


/* ALTERA BOTÃO DA LISTAGEM PARA PRODUTOS FORA DE ESTOQUE */
.outofstock a.button.product_type_simple {
    background: #000000 !important;
}


/* #nav_menu-2 a:nth-child(6){
    display: none;
} */

.widget-area#secondary #submenu a,
.textwidget a {
    background: black;
    color: white !important;
}

.widget-area#secondary #submenu a:hover {
    background: #eb2944;
}

.single_add_to_cart_button {
    background: #eb2944 !important;
    opacity: 1 !important;
}

.single_add_to_cart_button:hover {
    background: black !important
}

.menu-toggle {
    background: black !important;
}

button.menu-toggle:after,
button.menu-toggle:before,
button.menu-toggle span:before {
    background-color: white !important;
}

/*
.added_to_cart.alt, .added_to_cart, .widget a.button.checkout {
    background-color: #000;
    border-color: #000;
    display: block;
    width: 60%;
    margin: 0 auto;
}
*/

.woocommerce-cart button[name="update_cart"] {
    float: right;
    margin-left: 1rem;
}