jQuery(function(a){const b={init(){this.autofill("billing",!0),a(document.body).on("blur","#billing_postcode",function(){b.autofill("billing")}),a(document.body).on("blur","#shipping_postcode",function(){b.autofill("shipping")})},block(){a("form.checkout, form#order_review").addClass("processing").block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock(){a("form.checkout, form#order_review").removeClass("processing").unblock()},autofill(c,d){if(d=d||!1,a("#"+c+"_postcode").length){const e=a("#"+c+"_postcode").val().replace(".","").replace("-",""),f=a("#"+c+"_country").val(),g=a("#"+c+"_address_1").val(),h="yes"===WCCorreiosAutofillAddressParams.force||0===g.length;""!==e&&8===e.length&&"BR"===f&&h&&(b.block(),a.ajax({type:"GET",url:WCCorreiosAutofillAddressParams.url+"&postcode="+e,dataType:"json",contentType:"application/json",success(a){if(a.success&&(b.fillFields(c,a.data),d)){const d="billing"===c?"shipping":"billing";b.fillFields(d,a.data)}b.unblock()}}))}},fillFields(b,c){c.address&&a("#"+b+"_address_1").val(c.address).change(),c.neighborhood&&(a("#"+b+"_neighborhood").length?a("#"+b+"_neighborhood").val(c.neighborhood).change():a("#"+b+"_address_2").val(c.neighborhood).change()),a("#"+b+"_city").val(c.city).change(),a("#"+b+"_state").val(c.state).change()}};b.init()});