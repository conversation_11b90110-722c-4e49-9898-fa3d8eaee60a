<?php 
/** 
 * The sidebar containing the main widget area. 
 * 
 * @package storefront 
 */ 

if ( ! is_active_sidebar( 'sidebar-1' ) ) { 
    return; 
} 

$is_product = false; 
if ( is_single() ){ 
    $current_product = get_queried_object(); 
    if ( is_object( $current_product ) && isset( $current_product->post_type ) && $current_product->post_type == "product" ){ 
        $is_product = true; 
    } 
} 
if ( !$is_product ){ 
?> 
    <div id="secondary" class="widget-area" role="complementary"> 
    <?php do_action( 'antid_sub_category' ); ?>
            <?php dynamic_sidebar( 'sidebar-1' ); ?> 
    </div><!-- #secondary --> 
<?php } ?> 
