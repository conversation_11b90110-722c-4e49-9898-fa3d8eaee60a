<?php

function lmpjs_aplicar_ajuste_preco($tags, $percentual) {
    if (empty($tags) || !is_numeric($percentual)) return;

    $args = [
        'post_type' => 'product',
        'posts_per_page' => -1,
        'tax_query' => [
            [
                'taxonomy' => 'product_tag',
                'field'    => 'term_id',
                'terms'    => $tags,
            ]
        ]
    ];

    $query = new WP_Query($args);

    foreach ($query->posts as $product_post) {
        $product = wc_get_product($product_post->ID);

        // Atualiza o preço do produto principal
        $preco_atual = (float) $product->get_regular_price();
        if ($preco_atual > 0) {
            $novo_preco = round($preco_atual + ($preco_atual * ($percentual / 100)), 2);
            $product->set_regular_price($novo_preco);
        }

        // Atualiza o preço das variações, se houver
        if ($product->is_type('variable')) {
            $variations = $product->get_children(); // retorna os IDs das variações

            foreach ($variations as $variation_id) {
                $variation = wc_get_product($variation_id);
                $preco_variacao = (float) $variation->get_regular_price();

                if ($preco_variacao > 0) {
                    $novo_preco_variacao = round($preco_variacao + ($preco_variacao * ($percentual / 100)), 2);
                    $variation->set_regular_price($novo_preco_variacao);
                    $variation->save();
                }
            }
        }

        // Salva o produto principal
        $product->save();
    }
}


add_action('wp_ajax_lmpjs_buscar_tags_ajax', 'lmpjs_buscar_tags_ajax');
function lmpjs_buscar_tags_ajax() {
    if (!current_user_can('manage_woocommerce')) {
        wp_send_json_error('Sem permissão');
        wp_die();
    }

    $search = isset($_GET['q']) ? sanitize_text_field($_GET['q']) : '';

    $tags = get_terms([
        'taxonomy'   => 'product_tag',
        'hide_empty' => false,
        'name__like' => $search,
        'number'     => 20,
    ]);

    if (is_wp_error($tags)) {
        wp_send_json(['results' => []]);
    }

    $results = [];

    foreach ($tags as $tag) {
        $results[] = [
            'id'   => $tag->term_id,
            'text' => $tag->name
        ];
    }

    wp_send_json(['results' => $results]);
}

function lmpjs_obter_produtos_afetados($tags) {
    $args = [
        'post_type' => 'product',
        'posts_per_page' => -1,
        'tax_query' => [
            [
                'taxonomy' => 'product_tag',
                'field'    => 'term_id',
                'terms'    => $tags,
            ]
        ]
    ];

    $query = new WP_Query($args);
    return $query->posts;
}

function lmpjs_exibir_simulacao($produtos, $percentual) {
    echo '<h2>Simulação de Ajuste</h2>';
    echo '<table class="widefat"><thead><tr>
            <th>#</th>
            <th>Produto / Variação</th>
            <th>Preço Atual</th>
            <th>Novo Preço</th>
          </tr></thead><tbody>';

    $contador = 1;

    foreach ($produtos as $product_post) {
        $product = wc_get_product($product_post->ID);

        // Produto principal
        $preco_atual = (float) $product->get_regular_price();
        if ($preco_atual > 0) {
            $novo_preco = round($preco_atual + ($preco_atual * ($percentual / 100)), 2);
            echo '<tr>';
            echo '<td>' . $contador++ . '</td>';
            echo '<td><strong>' . esc_html($product->get_name()) . '</strong></td>';
            echo '<td>R$ ' . number_format($preco_atual, 2, ',', '.') . '</td>';
            echo '<td><strong>R$ ' . number_format($novo_preco, 2, ',', '.') . '</strong></td>';
            echo '</tr>';
        }

        // Se for produto variável, mostrar variações
        if ($product->is_type('variable')) {
            $variations = $product->get_children();

            foreach ($variations as $variation_id) {
                $variation = wc_get_product($variation_id);
                $preco_var = (float) $variation->get_regular_price();

                if ($preco_var > 0) {
                    $novo_preco_var = round($preco_var + ($preco_var * ($percentual / 100)), 2);

                    // Nome da variação
                    $attributes = $variation->get_attributes();
                    $attr_strings = [];
                    foreach ($attributes as $key => $value) {
                        $taxonomy = str_replace('attribute_', '', $key);
                        $term = get_term_by('slug', $value, $taxonomy);
                        if ($term) {
                            $attr_strings[] = $term->name;
                        } else {
                            $attr_strings[] = $value;
                        }
                    }
                    $nome_variacao = implode(', ', $attr_strings);

                    echo '<tr>';
                    echo '<td>' . $contador++ . '</td>';
                    echo '<td style="padding-left: 20px;">&raquo; ' . esc_html($product->get_name()) . ' - <em>' . esc_html($nome_variacao) . '</em></td>';
                    echo '<td>R$ ' . number_format($preco_var, 2, ',', '.') . '</td>';
                    echo '<td><strong>R$ ' . number_format($novo_preco_var, 2, ',', '.') . '</strong></td>';
                    echo '</tr>';
                }
            }
        }
    }

    echo '</tbody></table>';
}


