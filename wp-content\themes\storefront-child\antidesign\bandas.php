<?php

add_action( 'init', 'antid_band_taxonomies', 0 );

function antid_band_taxonomies() {
	$labels = array(
		'name'              => 'Bandas',
		'singular_name'     => 'Banda',
		'search_items'      => 'Pesquisar',
		'all_items'         => 'Bandas',
		'parent_item'       => null,
		'parent_item_colon' => null,
		'edit_item'         => 'Editar Banda',
		'update_item'       => 'Atualizar Banda',
		'add_new_item'      => 'Adicionar Banda',
		'new_item_name'     => 'Nome da Banda',
		'menu_name'         => 'Bandas',
	);

	$args = array(
                'label'=>'Bandas',
                'capabilities'          => array(
					'manage_terms' => 'manage_product_terms',
					'edit_terms'   => 'edit_product_terms',
					'delete_terms' => 'delete_product_terms',
					'assign_terms' => 'assign_product_terms',
                ),
		'hierarchical'      => true,
		'labels'            => $labels,
		'show_ui'           => true,
		'show_admin_column' => true,
                'public'            => true,
		'query_var'         => true,
		'rewrite'           => array( 'slug' => 'banda' ),
	);

	register_taxonomy( 'banda', array( 'product','post' ), $args );
}


add_filter( 'woocommerce_get_breadcrumb', 'antid_breadcrumb_woocommerce' );
function antid_breadcrumb_woocommerce( $defaults ) {
        if (is_tax("banda")){
            $term = get_queried_object();
            $title = $term->name;
            
            $defaults[1] = array('Bandas',site_url().'/bandas');
            $defaults[2] = array($title);
        }
	return $defaults;
}

add_filter('body_class','antid_body_class_full');
function antid_body_class_full($classes = '',$extra='') {
    
    if (is_tax("banda")){
        $classes[] = 'storefront-full-width-content';
        
    }
    //print_r($classes);
    return $classes;
}

function antid_loop_columns($per_row) {
    if (is_tax("banda")){
        $per_row = 4;
    }
return $per_row;
}
add_filter('loop_shop_columns', 'antid_loop_columns', 999);


add_action('bandas','bandas');

function bandas(){
    $archive_pages_args = array(
        'sort_order' => 'ASC',
	'sort_column' => 'post_title',
        'meta_key' => '_wp_page_template',
        'meta_value' => 'banda.php'
    );
    $html = '';
        $archive_pages = get_terms('banda',array('orderby'=>'name','order'=>'ASC'));
	$bandas = array();
        
	foreach($archive_pages as $page_banda){
            //print_r($page_banda);
            //echo '<br><br>';
            $term_id = $page_banda->term_id;
            $title = $page_banda->name;
            $link = get_term_link($page_banda);
            
            if ($title != ''){
                    $first_letter = $title[0];
                    if (!isset($bandas[$first_letter])){
                            $bandas[$first_letter] = '';
                    }
                    if ($link != "" && $title != ""){
                            $bandas[$first_letter] .= '<li><a href="'.$link.'">'.$title.'</a></li>';
                    }
            }
	}
	
    
    
    $html_goto = '';
    foreach($bandas as $index=>$lista){
        if ($lista != ''){
            $html_goto .= '<a href="#'.$index.'">'.strtoupper($index).'</a> ';
            $html .= '<div class="letra-box" id="'.$index.'"><div class="letra">'.strtoupper($index).'</div><ul class="order-'.$index.'">';
        }

        $html .= $lista;
        if ($lista != ''){
            $html .= '</ul></div>';
        }
    }
    $html_goto = '<div class="abcd">'.$html_goto.'</div>';
    echo $html_goto;
    echo $html;
}

?>