<?php
/*
Plugin Name: Lmpjs Tag Preço
Description: Ajuste de preços em massa com base nas tags de produto.
Version: 1.0
Author: <PERSON><PERSON><PERSON>
*/

if (!defined('ABSPATH')) exit;

define('LMPJS_TAG_PRECO_PATH', plugin_dir_path(__FILE__));

// Includes
require_once LMPJS_TAG_PRECO_PATH . 'includes/functions.php';
require_once LMPJS_TAG_PRECO_PATH . 'admin/settings-page.php';

// Scripts e estilos do Select2 no painel admin
add_action('admin_enqueue_scripts', 'lmpjs_enqueue_admin_scripts');
function lmpjs_enqueue_admin_scripts($hook) {
    if ($hook !== 'toplevel_page_lmpjs-tag-preco') return;

   wp_enqueue_style('select2-css', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
   wp_enqueue_script('select2-js', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', ['jquery'], null, true);

      // Certifica-se de que o Select2 está carregado
      wp_enqueue_script( 'select2' );
      wp_enqueue_style( 'select2' );

    $inline_script = <<<JS
    jQuery(document).ready(function($) {
        $("#lmpjs_tags_select").select2({
            ajax: {
                url: ajaxurl,
                dataType: "json",
                delay: 250,
                data: function (params) {
                    return {
                        action: "lmpjs_buscar_tags_ajax",
                        q: params.term
                    };
                },
                processResults: function (data) {
                    return data;
                },
                cache: true
            },
            minimumInputLength: 3,
            placeholder: "Busque e selecione tags de produto",
            width: "100%"
        });
    });
    JS;

    wp_add_inline_script('select2-js', $inline_script);

}


function lpmjs_price_tag_css() {
  echo '<style>
      .lmpjs_price textarea.select2-search__field {
         min-width: 250px;
      }

      .lmpjs_price .select2-search {
         margin-top: -19px;
      }

      .select2-selection__choice {
         padding: 0px 10px 2px 20px !important;
         position: relative !important;
      }

      .select2-selection__choice__remove {
         top: 42% !important;
         transform: translateY(-50%);
         border: 0 !important;
      }

      .lmpjs-price-input {
         width: 120px;
      }
      
  </style>';
}
add_action('admin_head', 'lpmjs_price_tag_css');