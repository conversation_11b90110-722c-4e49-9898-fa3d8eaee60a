<?php 

function antid_loop_columns($per_row) {
   if (is_tax("banda")){
       $per_row = 4;
   }
return $per_row;
}
add_filter('loop_shop_columns', 'antid_loop_columns', 999);



function antid_voltar_para_loja($url){
   $url = site_url() . '/';
   return $url;
}
add_filter("woocommerce_return_to_shop_redirect", "antid_voltar_para_loja" );


/* ----- Filtro de produtos em destaque no painel administrativo --- */
function wpa104537_filter_products_by_featured_status() {
     global $typenow, $wp_query;
     $output = "";
     
    if ($typenow=='product') :

        // Featured/ Not Featured
        $output .= "<select name='featured_status' id='dropdown_featured_status'>";
        
        $output .= '<option value="">Filtrar por destaque</option>';
        

        $output .="<option value='featured' ";
        
        if ( isset( $_GET['featured_status'] ) ) $output .= selected('featured', $_GET['featured_status'], false);
        
        $output .=">Destaque</option>";
        

        $output .="<option value='normal' ";
        
        if ( isset( $_GET['featured_status'] ) ) $output .= selected('normal', $_GET['featured_status'], false);
        
        $output .=">Não Destaque</option>";
        

        $output .="</select>";
                
        echo $output;
        
    endif;
    
}
add_action('restrict_manage_posts', 'wpa104537_filter_products_by_featured_status', 999);




function wpa104537_featured_products_admin_filter_query( $query ) {
    global $typenow;
    if ( $typenow == 'product' ) {
        // Subtypes
        if ( ! empty( $_GET['featured_status'] ) ) {

            if ( $_GET['featured_status'] == 'featured' ) {

                $query->query_vars['tax_query'][] = array(
                    'taxonomy' => 'product_visibility',
                    'field'    => 'slug',
                    'terms'    => 'featured',
                );
                
            }
             elseif ( $_GET['featured_status'] == 'normal' ) {

                $query->query_vars['tax_query'][] = array(
                    'taxonomy' => 'product_visibility',
                    'field'    => 'slug',
                    'terms'    => 'featured',
                    'operator' => 'NOT IN',
                );
            }
        }
    }
}
add_filter( 'parse_query', 'wpa104537_featured_products_admin_filter_query' );

/* ----- [FIM] -- Filtro de produtos em destaque no painel administrativo --- */
