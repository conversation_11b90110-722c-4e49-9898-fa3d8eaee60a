<?php
/**
 * Single Product Price
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/price.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * <AUTHOR>
 * @package WooCommerce/Templates
 * @version 3.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global $product;
$regular_price = $product->get_regular_price();
$sale_price = $product->get_sale_price();
?>
<div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
    <?php if ( empty( $sale_price ) || $regular_price == $sale_price ) : ?>
        <p class="price"><?php echo $product->get_price_html(); ?></p>
    <?php else: ?>
        <p class="price">
            <span class="row">
                    <del><b class="noline">De: R$&nbsp;</b><span class="amount"><?php echo $product->get_regular_price(); ?></span></del></span>
            <span class="row">
                    <ins><span class="amount"><b>Por: R$&nbsp;</b><?php echo $product->get_price(); ?></span></ins>
            </span>
        </p>
    <?php endif; ?>
    <meta itemprop="price" content="<?php echo $product->get_price(); ?>" />
    <meta itemprop="priceCurrency" content="<?php echo get_woocommerce_currency(); ?>" />
    <link itemprop="availability" href="http://schema.org/<?php echo $product->is_in_stock() ? 'InStock' : 'OutOfStock'; ?>" />

</div>
