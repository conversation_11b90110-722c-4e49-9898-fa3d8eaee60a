jQuery( function( $ ) {
    $(document.body).on( "country_to_state_changing", function( event, country, wrapper ) {
        var $postcodeField = wrapper.find('#billing_postcode_field, #shipping_postcode_field');
        var $addressField = wrapper.find('#billing_country_field, #shipping_country_field' );
        var fieldTimeout = setTimeout(function() {
            $postcodeField.insertAfter($addressField);
        }, 50);
    } );
} );