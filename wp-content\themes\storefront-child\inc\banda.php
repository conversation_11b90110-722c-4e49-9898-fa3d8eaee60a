<?php

function antid_band_taxonomies() {

   $labels = array(
       'name'              => 'Bandas',
       'singular_name'     => 'Banda',
       'search_items'      => 'Pesquisar',
       'all_items'         => 'Bandas',
       'parent_item'       => null,
       'parent_item_colon' => null,
       'edit_item'         => 'Editar Banda',
       'update_item'       => 'Atualizar Banda',
       'add_new_item'      => 'Adicionar Banda',
       'new_item_name'     => 'Nome da Banda',
       'menu_name'         => 'Bandas',
   );

   $args = array(
               'label'=>'Bandas',
               'capabilities'          => array(
                   'manage_terms' => 'manage_product_terms',
                   'edit_terms'   => 'edit_product_terms',
                   'delete_terms' => 'delete_product_terms',
                   'assign_terms' => 'assign_product_terms',
               ),
       'hierarchical'      => true,
       'labels'            => $labels,
       'show_ui'           => true,
       'show_admin_column' => true,
               'public'            => true,
       'query_var'         => true,
       'rewrite'           => array( 'slug' => 'banda' ),
   );

   register_taxonomy( 'banda', array( 'product','post' ), $args );
   
}

add_action( 'init', 'antid_band_taxonomies', 0 );


function antid_breadcrumb_woocommerce( $defaults ) {

    if (is_tax("banda")){

       $term = get_queried_object();
       
       $title = $term->name;
       

       $defaults[1] = array('Bandas',site_url().'/bandas');
       
       $defaults[2] = array($title);
       
    }

   return $defaults;
   
}

add_filter( 'woocommerce_get_breadcrumb', 'antid_breadcrumb_woocommerce' );



function antid_body_class_full($classes = '',$extra='') {


   if (is_tax("banda")){

       $classes[] = 'storefront-full-width-content';
       

   }

   //print_r($classes);
   
   return $classes;
   
}

add_filter('body_class','antid_body_class_full');


function bandas(){

   $archive_pages_args = array(
       'sort_order' => 'ASC',
  	   'sort_column' => 'post_title',
       'meta_key' => '_wp_page_template',
       'meta_value' => 'banda.php'
   );
   
   $html = '';
   
       $archive_pages = get_terms('banda',array('orderby'=>'name','order'=>'ASC'));
       
   $bandas = array();
   

   foreach($archive_pages as $page_banda){

           //print_r($page_banda);
   	
           //echo '<br><br>';
   	
           $term_id = $page_banda->term_id;
           
           $title = $page_banda->name;
           
           $link = get_term_link($page_banda);
           
           $translate_chars = array(
                'Š'=>'S', 'š'=>'s', 'Ž'=>'Z', 'ž'=>'z', 'À'=>'A', 'Á'=>'A', 'Â'=>'A', 'Ã'=>'A', 'Ä'=>'A', 'Å'=>'A', 'Æ'=>'A', 'Ç'=>'C', 'È'=>'E', 'É'=>'E','Ê'=>'E', 'Ë'=>'E', 'Ì'=>'I', 'Í'=>'I', 'Î'=>'I', 'Ï'=>'I', 'Ñ'=>'N', 'Ò'=>'O', 'Ó'=>'O', 'Ô'=>'O', 'Õ'=>'O', 'Ö'=>'O', 'Ø'=>'O', 'Ù'=>'U', 'Ú'=>'U', 'Û'=>'U', 'Ü'=>'U', 'Ý'=>'Y', 'Þ'=>'B', 'ß'=>'Ss', 'à'=>'a', 'á'=>'a', 'â'=>'a', 'ã'=>'a', 'ä'=>'a', 'å'=>'a', 'æ'=>'a', 'ç'=>'c', 'è'=>'e', 'é'=>'e', 'ê'=>'e', 'ë'=>'e', 'ì'=>'i', 'í'=>'i', 'î'=>'i', 'ï'=>'i', 'ð'=>'o', 'ñ'=>'n', 'ò'=>'o', 'ó'=>'o', 'ô'=>'o', 'õ'=>'o', 'ö'=>'o', 'ø'=>'o', 'ù'=>'u', 'ú'=>'u', 'û'=>'u', 'ý'=>'y', 'þ'=>'b', 'ÿ'=>'y'
            );
           
           
           if ($title != ''){

                $unidentified = false;
                
                $title_translated = strtr( $title, $translate_chars );
                
                $first_letter = $title_translated[0];
                
                if( !preg_match( "/[0-9a-zA-Z]/", $first_letter ) ){

                    $first_letter = "Outros";
                    
                }

                if (!isset($bandas[$first_letter])){

                        $bandas[$first_letter] = '';
                        
                }

                if ($link != "" && $title != ""){

                        $bandas[$first_letter] .= '<li><a href="'.$link.'">'.$title.'</a></li>';
                        
                }

           }

   }


   $bandas_outros = $bandas["Outros"];
   
   unset( $bandas["Outros"] );
   
   $bandas["Outros"] = $bandas_outros;
   

   $html_goto = '';
   
   foreach($bandas as $index=>$lista){

       if ($lista != ''){

           $html_goto .= '<a href="#'.$index.'" data-id="'.$index.'">'.strtoupper($index).'</a> ';
           
           $html .= '<div class="letra-box" id="'.$index.'"><div class="letra">'.strtoupper($index).'</div><ul class="order-'.$index.'">';
           
       }


       $html .= $lista;
       
       if ($lista != ''){

           $html .= '</ul></div>';
           
       }

   }

   $html_goto = '<div class="abcd">'.$html_goto.'</div>';
   
   echo $html_goto;
   
   echo $html;
   
}

add_action('bandas','bandas');


/*
function bandas_script_enqueue() {

    if( function_exists( "get_queried_object" ) ){

        $queried_obj = get_queried_object();
        
        if( is_object( $queried_obj ) && isset( $queried_obj->post_type ) && $queried_obj->post_type === "page" && isset( $queried_obj->post_name ) && $queried_obj->post_name === "bandas" ){

            wp_register_script( 'bandas_script', get_stylesheet_directory_uri() . "/js/bandas.js", array( 'jquery' ) );
            
            wp_enqueue_script( 'bandas_script' );
            

        }

    }

}

add_action( 'wp_enqueue_scripts', 'bandas_script_enqueue' );
*/
