<?php  

// Remove a barra de administração no frontend
add_filter('show_admin_bar', '__return_false');

// Display 20 products per page.
function new_loop_shop_per_page ($cols) {
    // $ cols contém o número atual de produtos por página com base no valor armazenado em Opções -> Leitura
    // Retorna o número de produtos que você deseja mostrar por página.
    $cols = 20;
    return $cols;
}
add_filter ('loop_shop_per_page', 'new_loop_shop_per_page', 20);


// Define 4 produtos por linha na grade da loja.
if (!function_exists('loop_columns')) {
    function loop_columns() {
        return 4; // 4 products per row
    }
}
add_filter('loop_shop_columns', 'loop_columns', 999);



// altera como os preços são exibidos (ex.: "A partir de...").
function custom_variable_price_html_optimized( $price, $product ) {
    // Certifica-se de que é um produto variável e o objeto $product é válido.
    if ( $product->is_type( 'variable' ) && is_a( $product, 'WC_Product_Variable' ) ) {

        // Obtém o preço mínimo do produto variável.
        // Este método já considera o preço de venda (desconto) se aplicável.
        $min_price = $product->get_price();

        // Obtém o preço máximo para verificar se há um range de preços.
        // O 'false' no segundo parâmetro impede que os impostos sejam incluídos,
        // o que é comum para a exibição "a partir de".
        $max_price = $product->get_variation_price( 'max', false );

        // Se o preço mínimo for vazio ou zero (para evitar "A partir de R$0,00" ou erros)
        if ( ! $min_price && $min_price !== 0.0 ) { // Verifica se é nulo ou false, mas permite 0.0
            return $price; // Retorna o preço original fornecido pelo WooCommerce
        }

        // Formata o preço mínimo para exibição.
        $formatted_min_price = wc_price( $min_price );

        // Se o preço mínimo for diferente do preço máximo, significa que existe um range
        // de preços para as variações, então exibe "A partir de".
        if ( $min_price !== $max_price ) {
            $price = '<span class="from">' . __( 'A partir de', 'woocommerce' ) . ' </span>' . $formatted_min_price;
        } else {
            // Se todas as variações têm o mesmo preço, exibe apenas o preço.
            $price = $formatted_min_price;
        }

        return $price;
    }

    // Para outros tipos de produto (simples) ou se não for um WC_Product_Variable,
    // retorna o preço original que o WooCommerce já gerou.
    return $price;
}
add_filter( 'woocommerce_variable_price_html', 'custom_variable_price_html_optimized', 10, 2 );




// altera a forma de exibir atributos (em <ul> e <li>).
function custom_woocommerce_attribute( $html, $attribute, $values ) {
    $html = '<ul>';
    foreach ( $values as $value ) {
        $html .= '<li>' . $value . '</li>';
    }
    $html .= '</ul>';
    return $html;
}
add_filter( 'woocommerce_attribute', 'custom_woocommerce_attribute', 10, 3 );




// Modifica a consulta de produtos relacionados para usar a categoria ancestral principal.
function antid_related_args( $args ){
    global $product,$woocommerce;
    $_3_0 = false;
    $product_id = -1;
    if( is_object( $woocommerce ) && isset( $woocommerce->version ) && !empty( $woocommerce->version ) ){
        $version = $woocommerce->version;
        if ( version_compare( $version, "3.0", '>=' ) ){
            $_3_0 = true;
        }
    }
    if( $_3_0 ){
        if ( is_object( $product ) && method_exists( $product, "get_id" ) ){
            $product_id = $product->get_id();
        }
    }
     else {
        if ( is_object( $product ) && isset( $product->id ) ){
            $product_id = $product->id;
        }
    }
    if ( $product_id > 0 ){
       $id = $product_id;
       $terms = get_the_terms( $id, "product_cat" );
       $parent = 0;
       $last_parent = $parent;
        while ( $parent != -1 ){
           $last_parent = $parent;
           $parent = antid_getParent( $parent, $terms );
        }
    }
    if ( $last_parent != 0 && $last_parent != -1 ){
       $args['tax_query'] = array(
            array(
               'taxonomy'  => 'product_cat',
                           'field'     => 'term_id',
               'terms'     => $last_parent
            ),
       );
    }
    unset( $args['post__in'] );
    return $args;
}
add_filter( 'woocommerce_related_products_args', 'antid_related_args' );


/**
 * Essa função está associada a outras funções
 * incluindo a de cima
 */
function antid_getParent( $parent=0, $terms=array() ){
    $return = -1;
    foreach( $terms as $term ){
        if ( is_object( $term ) && isset( $term->parent ) && isset( $term->term_id ) && $term->parent == $parent ){
            $return = $term->term_id;
        }
    }
    return $return;
}



// Produtos relacionados para serem exibidos em ordem aleatória.
function antid_random_orderby_related($args){
    if ( is_array( $args ) && isset( $args["post_type"] ) && "product" == $args["post_type"] ){
        $args['orderby'] = "rand";
    }
    return $args;
}
add_filter( 'woocommerce_related_products_args', 'antid_random_orderby_related', 1002 );


/**
 * Change the strength level on the woocommerce password
 *
 * https://www.webroomtech.com/remove-password-strength-checker-wordpress-woocommerce/
 *
 * Strength Settings
 * 4 = Strong
 * 3 = Medium (default) 
 * 2 = Also Weak but a little stronger 
 * 1 = Password should be at least Weak
 * 0 = Very Weak / Anything
 */
function webroom_change_password_strength( $strength ) {
     return 2;
}
add_filter( 'woocommerce_min_password_strength', 'webroom_change_password_strength' );


class PrecosCDArchive extends WP_Widget {
    public function __construct(){ parent::__construct( false, $name = 'Preços CDS' ); }
    public function widget($argumentos, $instancia) {
        $current_cat = get_queried_object();
        $term_slug = 'cds';
        if ( is_object( $current_cat ) ){
            if ( isset( $current_cat->taxonomy ) && "product_cat" == $current_cat->taxonomy ){
                if ( isset( $current_cat->parent ) && 0 != $current_cat->parent ){
                    while ( $current_cat->parent != 0 ){
                        $parent = $current_cat->parent;
                        $current_cat = get_term( $parent, "product_cat" );
                    }
                }
                if ( isset( $current_cat->slug ) && $current_cat->slug == $term_slug ){
                    $arr_tags = array(
                            '10-00' => '10,00',
                            '15-00' => '15,00',
                            '17-00' => '17,00',
                            '20-00' => '20,00',
                            '25-00' => '25,00'
                        );
                    echo $argumentos['before_widget'];
                    echo $argumentos['before_title'] . 'Preços' . $argumentos['after_title'];
                    foreach( $arr_tags as $slug_str => $tag ){
                        $tag_obj = get_term_by( 'slug', $slug_str, 'product_tag' );
                        if( is_object( $tag_obj ) ){
                            $tag_class = get_class( $tag_obj );
                            if ( $tag_class != "WP_Error" ){
                                $tag_link = get_term_link( $tag_obj, "product_tag" );
                                if( !is_object( $tag_link ) ){
                                    echo '<a href="'.$tag_link.'" class="botao easy">'.$tag.'</a>';
                                }
                            }
                        }
                    }
                    echo $argumentos['after_widget'];
                }
            }
        }
    }
}
add_action('widgets_init', function() {return register_widget("PrecosCDArchive"); },999);



function antid_fix_related_args($args){
    $args['columns'] = 4;
    return $args;
}
add_filter( 'woocommerce_upsell_display_args', 'antid_fix_related_args', 99999 );


function antid_fix_crosssell_args($columns){
    return 2;
}
add_filter( 'woocommerce_cross_sells_columns', 'antid_fix_crosssell_args', 99999 );


function antid_my_styles_method() {
    $custom_css = "
            .woocommerce-cart.page-template-template-fullwidth-php .cart-collaterals .cross-sells {
                width:100%;
            }
            .woocommerce-cart.page-template-template-fullwidth-php .cart-collaterals .cross-sells ul.products li.product {
                width:18.18%;
            }";
    wp_add_inline_style( 'storefront-child-style', $custom_css );
}
add_action( 'wp_enqueue_scripts', 'antid_my_styles_method', 9999 );


// Essa função altera a query principal para ordenar produtos com estoque primeiro
function antid_orderby_popularidade_produtos_indisponiveis( $args, $query ){
    if ( $query->is_main_query() ){
        $post_type = get_query_var( "post_type", "" );
        $add_to_query = false;
        if( is_product_taxonomy() || is_post_type_archive( "product" ) ){
            $add_to_query = true;
        } else if( is_search() && !empty( $post_type ) && $post_type === "product" ){
            $add_to_query = true;
        }
        if ( ( is_object( $query ) && isset( $query->query_vars ) && is_array( $query->query_vars ) && isset( $query->query_vars["meta_key"] ) && ( "total_sales" == $query->query_vars["meta_key"] || "_price" == $query->query_vars["meta_key"] ) ) || $add_to_query ){
            global $wpdb;
            $args['join'] .= " LEFT JOIN " . $wpdb->postmeta . " AS pmx ON ( {$wpdb->posts}.ID = pmx.post_id AND pmx.meta_key = '_stock_status' ) ";
            $antid_orderby = "( CASE pmx.meta_value WHEN 'instock' THEN 0 ELSE 1 END ) ASC ";
            if ( $args['orderby'] != "" ){
                $args['orderby'] = "," . $args['orderby'];
            }
            $args['orderby'] = $antid_orderby . $args['orderby'];
        }
    }
    return $args;
}

add_filter( 'posts_clauses', 'antid_orderby_popularidade_produtos_indisponiveis', 9999, 2 );




// Desativa o script storefront-sticky-payment, que mantém o botão de pagamento fixo na tela no tema Storefront.
function antid_dequeue_sticky_payment() {
   wp_dequeue_script( 'storefront-sticky-payment' );
}
add_action( 'wp_enqueue_scripts', 'antid_dequeue_sticky_payment', 9999 );

// Personaliza a mensagem exibida no checkout referente à aplicação de cupons.
function antidesign_update_coupon_msg($msg){
    $msg = 'Se você possuir um cupom de desconto, <a href="#" class="showcoupon">clique aqui</a> para informar o código.';
    return $msg;
}
add_filter( 'woocommerce_checkout_coupon_message', "antidesign_update_coupon_msg" );

// Remove e re-adiciona o total do carrinho com prioridade menor (mais cedo).
remove_action( 'woocommerce_cart_collaterals', 'woocommerce_cart_totals', 10 );
add_action( 'woocommerce_cart_collaterals', 'woocommerce_cart_totals', 9 );


// Adiciona o código de erro 011 (normalmente “área de risco”) à lista de códigos aceitos pelo plugin WooComemrce Correios
// Evita que a entrega falhe por esse motivo — o plugin tratará a área de risco como válida.
function antidesign_fix_correios_area_risco( $codes = "" ){
    $new_code = "011";
    if( is_array( $codes ) && !in_array( $new_code, $codes ) ){
        array_push( $codes, $new_code );
    }
    return $codes;
}
add_filter('woocommerce_correios_accepted_error_codes', "antidesign_fix_correios_area_risco", PHP_INT_MAX);





// Oculta os campos "estado" e "cidade" do calculador de frete na página do carrinho do WooCommerce.
// O usuário  verá apenas o campo de CEP no cálculo de frete, simplificando o formulário.
function hide_shipping_calc_attributes(){
    return false;
}
add_filter( "woocommerce_shipping_calculator_enable_state", "hide_shipping_calc_attributes" );
add_filter( "woocommerce_shipping_calculator_enable_city", "hide_shipping_calc_attributes" );




// Remove o título padrão dos produtos exibidos no loop da loja
remove_action( 'woocommerce_shop_loop_item_title', 'woocommerce_template_loop_product_title', 10 );

// Remove o menu lateral de navegação da conta do cliente.
remove_action( 'woocommerce_account_navigation', 'woocommerce_account_navigation' );


// Substitui o conteúdo do Dashboard da conta do cliente por uma simulação do endpoint de pedidos,
// passando 1 como parâmetro (possivelmente para paginação ou controle interno).
add_action( "woocommerce_account_dashboard", function(){
    if ( has_action( 'woocommerce_account_orders_endpoint' ) ) {
        do_action( 'woocommerce_account_orders_endpoint', 1 );   
    }
});

/**
 * Modifica os parâmetros da consulta de pedidos no "Minha Conta" quando não estamos diretamente no endpoint orders.
 * Adiciona flags personalizadas (antd_add_total, antd_add_recent_order_title, antd_break_page) para serem interpretadas posteriormente.
 */
add_filter( "woocommerce_my_account_my_orders_query", function( $args = "" ){
    if( !is_wc_endpoint_url( 'orders' ) ){
        $args["limit"] = 15;
        $args["paginate"] = false;
        $args["antd_add_total"] = true;
        $args["antd_add_recent_order_title"] = true;
        $args["antd_break_page"] = true;
    }
    return $args;
} );

// Interpreta as flags adicionadas anteriormente.
add_filter( "woocommerce_order_query", function( $results = "", $args = "" ){
    if( is_array( $args ) && isset( $args["antd_add_total"] ) && $args["antd_add_total"] && isset( $args["paginate"] ) && !$args["paginate"] ){
        if( is_array( $results ) ){
            $new_results = new stdClass();
            $new_results->orders = $results;
            $new_results->total = strval( count( $results ) );
            $new_results->max_num_pages = floatval( 1 );
            $new_results->add_recent_order_title = false;
            $new_results->antd_break_page = false;
            if( count( $results ) > 0 ){
                if( isset( $args["antd_add_recent_order_title"] ) && $args["antd_add_recent_order_title"] ){
                    $new_results->add_recent_order_title = true;
                }
            }else{
                if( isset( $args["antd_break_page"] ) && $args["antd_break_page"] ){
                    $new_results->antd_break_page = true;
                }
            }
            return $new_results;
        }
    }
    return $results;
}, 999, 2 );



/**
 * garante que o país de envio (shipping_country) de um cliente sempre seja válido, de acordo com os países habilitados em WooCommerce.
 */
add_filter( "woocommerce_customer_get_shipping_country", function( $value = "", $customer = "" ){
    if( function_exists( "WC" ) ){
        $wc = WC();
        if( is_object( $wc ) && isset( $wc->countries ) && is_object( $wc->countries ) && method_exists( $wc->countries, "get_shipping_countries" ) ){
            $countries_enabled = $wc->countries->get_shipping_countries();
            if( empty( $value ) ){
                if( is_array( $countries_enabled ) && !empty( $countries_enabled ) ){
                    foreach( $countries_enabled as $key => $value_now ){
                        if( !empty( $key ) ){
                            $value = $key;
                            break;
                        }
                    }
                }
            } else {
                if( is_array( $countries_enabled ) && !empty( $countries_enabled ) ){
                    $found = false;
                    $first_country = "";
                    foreach( $countries_enabled as $key => $value_now ){
                        if( !empty( $key ) && empty( $first_country ) ){
                            $first_country = $key;
                        }
                        if( !empty( $key ) && $key == $value ){
                            $found = true;
                            break;
                        }
                    }
                    if( !$found ){
                        $value = $first_country;
                    }
                }
            }
        }
    }
    return $value;
}, 10, 2 );


/**
 * Permite que pedidos com status cancelled sejam refeitos (funcionalidade "Pedir novamente").
 */
add_filter( "woocommerce_valid_order_statuses_for_order_again", function( $order_types = "" ){
    if( is_array( $order_types ) && !empty( $order_types ) && !in_array( "cancelled", $order_types ) ){
        array_push( $order_types, "cancelled" );
    }
    return $order_types;
} );


/******
 * 
 * adicionar classes CSS adicionais ao elemento <body> de páginas
 * de categorias de produtos ou de produtos individuais
 * 
 */
function browser_body_class($classes = '') {
      if (is_product_category()){
          $current_category = get_queried_object();
          $parent__ = $current_category->parent;
          if ($parent__ <> 0){
              $current_category = get_term($current_category->parent,'product_cat');
          }
          if ($current_category->slug != ""){
              $classes[] = ''.$current_category->slug;
          }
      } else if (is_product()){
          $post = get_queried_object();
          if( is_object( $post ) && isset( $post->ID ) ){
              $terms = get_the_terms( $post->ID, 'product_cat' );
                foreach ($terms as $term) {
                    $product_cat_id = $term->term_id;
                    $___parent = $term->parent;
                    if ($term->parent == 0){
                        $classes[] = ''.$term->slug;
                    }
                }
          }
      }
  return $classes;
}
add_filter('body_class','browser_body_class');


//Reposition WooCommerce breadcrumb 
function jk_remove_storefront_breadcrumb() {
    remove_action( 'storefront_content_top', 'woocommerce_breadcrumb',  20 );
}
add_action( 'init', 'jk_remove_storefront_breadcrumb' );

/**
 * Encapsula o breadcrumb do WooCommerce em uma action customizada.
 * Chamar no front: do_action( 'woo_custom_breadcrumb' ); 
 */
function woocommerce_custom_breadcrumb(){
    woocommerce_breadcrumb();
}
add_action( 'woo_custom_breadcrumb', 'woocommerce_custom_breadcrumb', 20 );


// Customize Woocommerce Related Products Output
// Display 4 products in 4 columns
function woocommerce_output_related_products() {
    woocommerce_related_products( array( "posts_per_page" => 4, "columns" => 4 ) );   
}


// Remove abas da página de produtos
function woo_remove_product_tabs( $tabs ) {
    //unset( $tabs['description'] );        // Remove the description tab
    //unset( $tabs['reviews'] );            // Remove the reviews tab
    unset( $tabs['additional_information'] );   // Remove the additional information tab
    return $tabs;
}
add_filter( 'woocommerce_product_tabs', 'woo_remove_product_tabs', 98 );




/**
* Este filtro personalizado controla a exibição de variações
* esgotadas de um produto no WooCommerce, provavelmente em um contexto customizado.
 */
add_filter( "antidesign_display_variable_outofstock", function( $display = false, $product = "" ){
    if( !$display ){
        if( is_object( $product ) ){
            $outofstock = check_stock_pedidos( $product, true );
            if ( $outofstock ){
                $display = true;
            }
        }
    }
    return $display; 
}, 10, 2 );

/**
 * Controlar a exibição de produtos fora de estoque no front-end (em listagens, por exemplo), de forma customizada.
 */
add_filter( "antidesign_display_outofstock", function( $display = false, $product = "" ){
    if( !$display ){
        if( is_object( $product ) ){
            $outofstock = check_stock_pedidos( $product, false );
            if ( $outofstock ){
                $display = true;
            }
        }
    }
    return $display;
}, 10, 2 );

/**
 * modificar a informação de disponibilidade em estoque (is_in_stock) das variações de produtos, baseado em uma verificação customizada
 */
add_filter( "woocommerce_available_variation", function( $variation_data = array(), $product = "", $variation = "" ){
    if( is_array( $variation_data ) && isset( $variation_data["is_in_stock"] ) ){
        if( $variation_data["is_in_stock"] ){
            if( is_object( $variation ) ){
                $outofstock = check_stock_pedidos( $variation, true );
                if ( $outofstock ){
                    $variation_data["is_in_stock"] = false;
                }
            }
        }
    }
    return $variation_data;
}, 9999, 3 );



/**
 * função utiliza por outras funções
 */
function check_stock_pedidos($product,$variable = false, $check_qty = 1 ){
    $_product = $product;
    $not_enough_stock = false;
    global $wpdb;
    $order_id = 0;
    $qtde_produtos = antid_get_stock_qtde( $product, $variable );
    if ( $_product->get_stock_quantity() < ( $qtde_produtos + $check_qty ) ) {
        $not_enough_stock = true;
    }
    return $not_enough_stock;
}

/**
 * função utiliza por outras funções
 */
function antid_get_stock_qtde( $product, $variable = false ){
    $_product = $product;
    $not_enough_stock = false;
    global $wpdb;
    $order_id = 0;
    $qtde_pedidos = 0;
    $remove_from_payment_method = apply_filters( 'antidesign_remove_payment_method_count_indisponivel', array( 'bacs' ) );
    $where_payment_method = "";
    if( is_array( $remove_from_payment_method ) && count( $remove_from_payment_method ) > 0 ){
        $where_payment_method = " AND ( wp_pm1.meta_value IS NULL OR wp_pm1.meta_value NOT IN ('".implode( "','", $remove_from_payment_method )."') )";
    }
    global $woocommerce;
    if ( $variable ){
        $variation_id = -1;
        $parent_id = -1;
        if( is_object( $woocommerce ) && isset( $woocommerce->version ) && !empty( $woocommerce->version ) ){
            $version = $woocommerce->version;
            if( version_compare( $version, "3.0", '>=' ) ){
                if( is_object( $_product ) && method_exists( $_product, "get_parent_id" ) && method_exists( $_product, "get_id" ) ){
                    $variation_id = $_product->get_id();
                    $parent_id = $_product->get_parent_id();
                }
            } else {
                if( is_object( $_product ) && isset( $_product->parent ) && is_object( $_product->parent ) && isset( $_product->parent->post ) && is_object( $_product->parent->post ) && isset( $_product->parent->post->ID ) ){
                    if (isset( $_product->variation_id ) ){
                        $variation_id = $_product->variation_id;
                        $parent_id = $_product->parent->post->ID;
                    }
                }
            }
        }        
        if( $variation_id > 0 && $parent_id > 0 ){   
            $held_stock = $wpdb->get_var(
                $wpdb->prepare( "
                    SELECT SUM( order_item_meta.meta_value ) AS held_qty
                    FROM {$wpdb->posts} AS posts
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_items as order_items ON posts.ID = order_items.order_id
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta ON order_items.order_item_id = order_item_meta.order_item_id
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta2 ON order_items.order_item_id = order_item_meta2.order_item_id
                                                LEFT JOIN {$wpdb->prefix}postmeta wp_pm1 ON wp_pm1.post_id = posts.ID AND wp_pm1.meta_key = '_payment_method'
                    WHERE     order_item_meta.meta_key   = '_qty'
                    AND     order_item_meta2.meta_key  = %s AND order_item_meta2.meta_value  = %d
                    AND     posts.post_type            IN ( '" . implode( "','", wc_get_order_types() ) . "' )
                    AND     (posts.post_status = 'wc-pending'  OR posts.post_status = 'wc-on-hold' )
                                                " . $where_payment_method . "
                    AND        posts.ID                   != %d;",
                    '_variation_id',
                    $variation_id,
                    $order_id
                )
            );
            $qtde_pedidos = $held_stock;
        }
    }else{
        $product_id = -1;
        if( is_object( $woocommerce ) && isset( $woocommerce->version ) && !empty( $woocommerce->version ) ){
            $version = $woocommerce->version;
            if ( version_compare( $version, "3.0", '>=' ) ){
                if( is_object( $_product ) && method_exists( $_product, "get_id" ) ){
                    $product_id = $_product->get_id();
                }
            }else{
                if(  is_object( $_product ) && isset( $_product->id ) ){
                    $product_id = $_product->id;
                }
            }
        }
        if( $product_id != -1 ){
            $held_stock = $wpdb->get_var(
                $wpdb->prepare( "
                    SELECT SUM( order_item_meta.meta_value ) AS held_qty
                    FROM {$wpdb->posts} AS posts
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_items as order_items ON posts.ID = order_items.order_id
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta ON order_items.order_item_id = order_item_meta.order_item_id
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta2 ON order_items.order_item_id = order_item_meta2.order_item_id
                    LEFT JOIN {$wpdb->prefix}postmeta wp_pm1 ON wp_pm1.post_id = posts.ID AND wp_pm1.meta_key = '_payment_method'
                    WHERE     order_item_meta.meta_key   = '_qty'
                    AND     order_item_meta2.meta_key  = %s AND order_item_meta2.meta_value  = %d
                    AND     posts.post_type            IN ( '" . implode( "','", wc_get_order_types() ) . "' )
                    AND     (posts.post_status = 'wc-pending'  OR posts.post_status = 'wc-on-hold' )
                    " . $where_payment_method . "
                    AND        posts.ID                   != %d;",
                    '_product_id',
                    $product_id,
                    $order_id
                )
            );
            $qtde_pedidos = $held_stock;
        }
    }
    return $qtde_pedidos;
}



# LISTAGEM DE PRODUTOS - ORDENAÇÃO

/* Desabilita um modal na página do carrinho - Storefront */
add_filter ('storefront_sticky_add_to_cart', '__return_false');

// Remove itens do select nas páginas de listagem
function my_woocommerce_catalog_orderby( $orderby ) {
    unset($orderby["rating"]); // Remove "por avaliação"
    return $orderby;
}
add_filter( "woocommerce_catalog_orderby", "my_woocommerce_catalog_orderby", 20 );

// Adiciona a opção "Ordem alfabética" ao menu de ordenação
function antid_catalog_orderby($args){
    $new_args = array();
    $new_args['post_title'] = 'Ordem alfabética';
    foreach( $args as $index => $arg ){
        $new_args[$index] = $arg;
    }
    return $new_args;
}
add_filter( 'woocommerce_catalog_orderby', 'antid_catalog_orderby' );

// Define o comportamento da ordenação personalizada
function definir_ordenacao_padrao_woocommerce($args) {
    if ( is_shop() || is_product_category() ) {
        // Verifica se o usuário escolheu a ordenação personalizada
        if ( isset($_GET['orderby']) && $_GET['orderby'] === 'post_title' ) {
            $args['orderby'] = 'title';
            $args['order'] = 'ASC';
        } else {
            // Ordenação padrão
            $args['orderby'] = 'date';
            $args['order'] = 'DESC';
        }

        $args['meta_key'] = ''; // Limpa meta_key
    }

    return $args;
}
add_filter('woocommerce_get_catalog_ordering_args', 'definir_ordenacao_padrao_woocommerce');





// Exibir login e registro no checkout
// https://hmrock.com.br/finalizar-compra/
// add_action('woocommerce_before_checkout_form', 'custom_login_registration_checkout', 5);
function custom_login_registration_checkout() {
    // Não mostrar se o usuário já estiver logado
    if ( is_user_logged_in() ) return;
    
    echo '<div class="u-columns col2-set" id="customer_login">';
    echo '<div class="u-column2 col-2">';
    
    // Formulário de login
    if ( function_exists('woocommerce_login_form') ) {
        woocommerce_login_form(
            array(
                'message'  => 'Já tem uma conta? Faça login abaixo:',
                'redirect' => wc_get_checkout_url(),
            )
        );
    }
    echo '</div>';
    
    // Formulário de registro - usando abordagem alternativa
    echo '<div class="u-column2 col-2">';
    echo '<h3>Criar nova conta</h3>';
    
    // Verificar se o registro está habilitado
    if ( 'yes' === get_option( 'woocommerce_enable_myaccount_registration' ) ) {
        // Formulário de registro manual em vez de usar woocommerce_register_form()
        ?>
        <form method="post" class="register">
            <?php do_action( 'woocommerce_register_form_start' ); ?>
            
            <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                <label for="reg_email"><?php _e( 'Email address', 'woocommerce' ); ?> <span class="required">*</span></label>
                <input type="email" class="woocommerce-Input woocommerce-Input--text input-text" name="email" id="reg_email" autocomplete="email" value="<?php echo ( ! empty( $_POST['email'] ) ) ? esc_attr( wp_unslash( $_POST['email'] ) ) : ''; ?>" />
            </p>
            
            <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                <label for="reg_password"><?php _e( 'Password', 'woocommerce' ); ?> <span class="required">*</span></label>
                <input type="password" class="woocommerce-Input woocommerce-Input--text input-text" name="password" id="reg_password" autocomplete="new-password" />
            </p>
            
            <?php do_action( 'woocommerce_register_form' ); ?>
            
            <p class="woocommerce-form-row form-row">
                <?php wp_nonce_field( 'woocommerce-register', 'woocommerce-register-nonce' ); ?>
                <button type="submit" class="woocommerce-Button woocommerce-button button woocommerce-form-register__submit" name="register" value="<?php esc_attr_e( 'Register', 'woocommerce' ); ?>"><?php esc_html_e( 'Register', 'woocommerce' ); ?></button>
            </p>
            
            <input type="hidden" name="checkout_page" value="1"/>
            <?php do_action( 'woocommerce_register_form_end' ); ?>
        </form>
        <?php
    } else {
        echo '<p>' . __('Registration is currently disabled.', 'woocommerce') . '</p>';
    }
    
    echo '</div>'; // Fecha u-column2 col-2
    echo '</div>'; // Fecha customer_login
}
