var current_jQ = jQuery;
current_jQ( document ).ready(function() {
    current_jQ('div.banner.slick').slick({
		lazyLoad: 'ondemand',
        dots: true,
        autoplay: true,
        autoplaySpeed: 5000,
        pauseOnHover: false,
        fade: true,
        adaptiveHeight: false
    });
    current_jQ('div.banner.slick').on('init', function(event, slick, currentSlide, nextSlide){
        current_jQ('div.banner.slick').slick('slickPlay');
    });
});

current_jQ( document ).ready(function() {
    current_jQ('div.depoimentos').slick({
        dots: true,
        autoplay: true,
        autoplaySpeed: 5000,
        pauseOnHover: true,
        fade: false,
        adaptiveHeight: true
    });
    current_jQ('div.depoimentos').on('init', function(event, slick, currentSlide, nextSlide){
        current_jQ('div.depoimentos').slick('slickPlay');
    });
});

current_jQ( document ).ready(function() {
    current_jQ('div.galeria').slick({
        dots: true,
        autoplay: true,
        autoplaySpeed: 5000,
        pauseOnHover: false,
        fade: false,
        adaptiveHeight: true
    });
    current_jQ('div.galeria').on('init', function(event, slick, currentSlide, nextSlide){
        current_jQ('div.galeria').slick('slickPlay');
    });
});