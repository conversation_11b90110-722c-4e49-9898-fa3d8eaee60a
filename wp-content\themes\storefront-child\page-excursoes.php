<?php
/**
* The template for displaying the homepage.
*
* This page template will display any functions hooked into the `homepage` action.
* By default this includes a variety of product displays and the page content itself. To change the order or toggle these components
* use the Homepage Control plugin.
* https://wordpress.org/plugins/homepage-control/
*
* Template name: Página Excursões
*
* @package storefront
*/

get_header(); ?>
<?php $image = wp_get_attachment_image_src( get_post_thumbnail_id( $post->ID ), 'full' );?>
   <div id="primary" class="content-fullwidth">
           <div class="excursoes-box">
       <main id="main" class="site-main" role="main">
                   

           <?php while ( have_posts() ) : the_post(); ?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>


<?php if($image) { ?>
<div class="banner">
	<div class="banner-inner">
		<div class="banner-foto">
   		  	<img src="<?php echo $image[0]; ?>" class="foto"/>
   	  	</div>
	   	<div class="slider-title">
	   	<h1><span><?php the_title(); ?></span></h1>
		   	  	<?php if(get_field('titulo')) { ?>
					<h2><?php the_field('titulo'); ?></h2>
				<?php } else { ?>
				<?php } ?>
		</div>
	</div>
</div>

<?php do_action ( 'woo_custom_breadcrumb' ); ?>

<div class="entry-content container page-banner">
<?php } else { ?>


   <header class="entry-header">
       <h1 class="entry-title"><span><?php single_post_title(); ?></span></h1>
       <?php if(get_field('titulo')) { ?>
					<h2><?php the_field('titulo'); ?></h2>
				<?php } else { ?>
				<?php } ?>
   </header><!-- .page-header -->
   <?php do_action ( 'woo_custom_breadcrumb' ); ?>
   <div class="entry-content container">

<?php } ?>


<div class="chamada"><?php the_content(); ?></div>
<div class="lista">
   <ul class="excursoes-lista">
   <?php $loop = new WP_Query( array( 'post_type' => 'excursoes', 'posts_per_page' => -1, 'antid_organize_excursoes' => true ) ); ?>
   <?php while ( $loop->have_posts() ) : $loop->the_post(); ?>
   <?php $image = wp_get_attachment_image_src( get_post_thumbnail_id( $post->ID ), 'large' );?>
   <li>
   <div class="poster">
       <img src="<?php echo $image[0]; ?>" onload="this.style.opacity='1';" class="foto"/>
   </div>
   <div class="info">
	<?php
	$antid_data = get_field('antid_data');
	$antid_data_html = "";
	if ( $antid_data != "" ){
		$antid_ano = substr($antid_data, 0, 4);
		$antid_mes = substr($antid_data, 4, 2);
		$antid_dia = substr($antid_data, 6, 2);
		if( $antid_dia != "" && $antid_mes != "" && $antid_ano != "" ){
			$antid_data_arr = array ( $antid_dia, $antid_mes, $antid_ano );
			$antid_data_html = implode( '/', $antid_data_arr );
		}
	}
	?>
       <h3><?php if( $antid_data_html != "" ){ ?><span class="excursao-data"><?php echo $antid_data_html;?></span><?php } ?><?php the_title(); ?></h3>
       <?php the_content(); ?>
       <a href="#" class="reservar easy" data-id="<?php the_ID();?>">RESERVAR</a>
   </div>
   </li>
   <?php endwhile; wp_reset_query(); ?>
</ul> <!-- cd-testimonials -->

<div class="woocommerce-message excursoes-msg" style="display:none;">Sua reserva foi enviada, aguarde nosso contato.</div>

 <div class="excursoes-form" style="display:none;">
 	<div class="cadastro cadastro-excursoes">
 	  	<?php echo do_shortcode('[contact-form-7 id="7569" title="Excursões"]'); ?>
 	</div>
 </div>
 
 
</div>


       </div>
</article><!-- #post-## -->

           <?php endwhile; // end of the loop. ?>

       </main><!-- #main -->
           </div>
          
   </div><!-- #primary -->

<?php get_footer(); ?>

