<?php

/**
 * 
 * VERSÃO ATUALIZADA - 30/05/2025
 * + Segurança contra SQL Injection
 * + Performance
 */

add_filter('posts_where', 'search_only_product_tag_remove_excerpt', 999, 2);
add_filter('posts_clauses', 'antid_post_clauses_search', 999, 2);

// Busca por SKU e Tags
function antid_search_product_ids_by_sku_or_tag($search_query) {
    global $wpdb;

    $post_ids = [];

    // Busca por SKU
    $sku_query = $wpdb->prepare("
        SELECT post_id FROM {$wpdb->postmeta} pm
        INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
        WHERE pm.meta_key = '_sku'
        AND pm.meta_value LIKE %s
        AND p.post_status = 'publish'
        AND p.post_type = 'product'
    ", '%' . $wpdb->esc_like($search_query) . '%');

    $sku_results = $wpdb->get_col($sku_query);
    if (!empty($sku_results)) {
        $post_ids = array_merge($post_ids, $sku_results);
    }

    // Busca por Tag
    $tag_query = $wpdb->prepare("
        SELECT p.ID FROM {$wpdb->terms} t
        INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
        INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
        INNER JOIN {$wpdb->posts} p ON p.ID = tr.object_id
        WHERE tt.taxonomy = 'product_tag'
        AND t.name LIKE %s
        AND p.post_status = 'publish'
        AND p.post_type = 'product'
    ", '%' . $wpdb->esc_like($search_query) . '%');

    $tag_results = $wpdb->get_col($tag_query);
    if (!empty($tag_results)) {
        $post_ids = array_merge($post_ids, $tag_results);
    }

    return array_unique($post_ids);
}

// Filtro WHERE otimizado
function search_only_product_tag_remove_excerpt($where, $query) {
    if (is_search() && $query->is_main_query() && !is_admin()) {
        $search = $query->get('s');
        $post_ids = antid_search_product_ids_by_sku_or_tag($search);

        if (!empty($post_ids)) {
            $ids_str = implode(',', array_map('intval', $post_ids));
            $where .= " OR {$GLOBALS['wpdb']->posts}.ID IN ($ids_str)";
        }

        // Remove busca no excerpt
        $where = preg_replace("/OR \(post_excerpt\s+LIKE\s*(\'\%[^\%]+\%\')\)/", "", $where);
    }

    return $where;
}

// Filtro ORDER BY otimizado
function antid_post_clauses_search($pieces, $query) {
    if (is_search() && $query->is_main_query() && !is_admin()) {
        $search = $query->get('s');
        $post_ids = antid_search_product_ids_by_sku_or_tag($search);

        if (!empty($post_ids)) {
            $ids_str = implode(',', array_map('intval', $post_ids));
            $order = "(CASE WHEN wp_posts.ID IN ($ids_str) THEN 1 ELSE 0 END) DESC";

            $pieces['orderby'] = $order . (isset($pieces['orderby']) ? ', ' . $pieces['orderby'] : '');
        }
    }

    return $pieces;
}
