<?php
/**
 * The template used for displaying page content in page.php
 *
 * @package storefront
 */
?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
	<?php
	/**
	 * @hooked storefront_page_header - 10
	 * @hooked storefront_page_content - 20
	 */
	do_action( 'storefront_page' );
	?>
	<?php if(get_field('col_three')) { ?>
		<div class="entry-content colunas">
			<div class="three_col"><?php the_field('col_one'); ?></div>
			<div class="three_col"><?php the_field('col_two'); ?></div>
			<div class="three_col"><?php the_field('col_three'); ?></div>
	<?php } else { ?>
	
	<?php if(get_field('col_two')) { ?>
		<div class="entry-content colunas">
			<div class="two_col"><?php the_field('col_one'); ?></div>
			<div class="two_col"><?php the_field('col_two'); ?></div>
	<?php } else { ?><?php } ?>
	
	<?php } ?>
	
	
	
</article><!-- #post-## -->
