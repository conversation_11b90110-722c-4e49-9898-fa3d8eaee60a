<?php
/**
* The template for displaying the homepage.
*
* This page template will display any functions hooked into the `homepage` action.
* By default this includes a variety of product displays and the page content itself. To change the order or toggle these components
* use the Homepage Control plugin.
* https://wordpress.org/plugins/homepage-control/
*
* Template name: Página Selo HMR
*
* @package storefront
*/

get_header(); ?>
<?php $image = wp_get_attachment_image_src( get_post_thumbnail_id( $post->ID ), 'full' );?>
   <div id="primary" class="content-fullwidth">
           <div class="excursoes-box">
       <main id="main" class="site-main" role="main">
                   <div class="excursoes-msg" style="display:none;">
                       Dados enviados com sucesso.
                   </div>

           <?php while ( have_posts() ) : the_post(); ?>

<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>


<?php if($image) { ?>
<div class="banner">
	<div class="banner-inner">
		<div class="banner-foto">
   		  	<img src="<?php echo $image[0]; ?>" class="foto"/>
   	  	</div>
	   	<div class="slider-title">
	   	<h1><span><?php the_title(); ?></span></h1>
		   	  	<?php if(get_field('titulo')) { ?>
					<h2><?php the_field('titulo'); ?></h2>
				<?php } else { ?>
				<?php } ?>
		</div>
	</div>
</div>

<?php do_action ( 'woo_custom_breadcrumb' ); ?>

<div class="entry-content container page-banner">
<?php } else { ?>


   <header class="entry-header">
       <h1 class="entry-title"><span><?php single_post_title(); ?></span></h1>
       <?php if(get_field('titulo')) { ?>
					<h2><?php the_field('titulo'); ?></h2>
				<?php } else { ?>
				<?php } ?>
   </header><!-- .page-header -->
   <?php do_action ( 'woo_custom_breadcrumb' ); ?>
   <div class="entry-content container">

<?php } ?>


<div class="chamada"><?php the_content(); ?></div>

<div class="lista">
      <?php $loop = new WP_Query( array( 'post_type' => 'selo', 'posts_per_page' => -1 ) ); ?>
   <?php while ( $loop->have_posts() ) : $loop->the_post(); ?>
   <div class="row">
   <div class="selo-title">
       <h3><?php the_title(); ?></h3>
   </div>
   <div class="selo-lista">
       <?php the_content(); ?>
   </div>
   </div>
   <?php endwhile; wp_reset_query(); ?>

</div>


       </div>
</article><!-- #post-## -->

           <?php endwhile; // end of the loop. ?>

       </main><!-- #main -->
           </div>
          
   </div><!-- #primary -->

<?php get_footer(); ?>

