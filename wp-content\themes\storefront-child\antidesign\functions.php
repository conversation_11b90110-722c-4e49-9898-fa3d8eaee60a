<?php

$url_template = get_template_directory();
$url_template_url = get_template_directory_uri();
if ( file_exists( get_template_directory().'-child/antidesign/' ) ){
    $url_template = get_template_directory().'-child';
    $url_template_url = get_template_directory_uri().'-child';
}
$url_template_anti = $url_template . '/antidesign/';

add_action( 'admin_init', 'enqueue_all');
function enqueue_all(){
    wp_enqueue_style('thickbox'); // call to media files in wp
    wp_enqueue_script('thickbox');
    wp_enqueue_script( 'media-upload'); 
}

// load script to admin
function wpss_admin_js() {
    global $url_template_url;
     $siteurl = get_option('siteurl');
     $url = $url_template_url . '/antidesign/js/admin_script.js';
     echo "<script type='text/javascript' src='$url'></script>"; 
}
 add_action('admin_head', 'wpss_admin_js');

?>
