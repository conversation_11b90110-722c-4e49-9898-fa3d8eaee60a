<?php

/*

ESTÁ DESABILITADO NO ARQUIVO FUNCTIONS.PHP
TUDO ISSO FOI TROCADO PELO CONTACT FORM 7

*/

//antid_newsletter
add_action( 'init', 'antid_newsletter_init' );
function antid_newsletter_init() {
    $labels = array(
        'name'               => _x( 'E-mails', 'post type general name', 'your-plugin-textdomain' ),
        'singular_name'      => _x( 'E-mail', 'post type singular name', 'your-plugin-textdomain' ),
        'menu_name'          => _x( 'Newsletter', 'admin menu', 'your-plugin-textdomain' ),
        'name_admin_bar'     => _x( 'Newsletter', 'add new on admin bar', 'your-plugin-textdomain' ),
        'add_new'            => _x( 'Adicionar Nova', 'antid_area', 'your-plugin-textdomain' ),
        'add_new_item'       => __( 'Adicionar E-mail', 'your-plugin-textdomain' ),
        'new_item'           => __( 'Novo E-mail', 'your-plugin-textdomain' ),
        'edit_item'          => __( 'Editar E-mail', 'your-plugin-textdomain' ),
        'view_item'          => __( 'Visualizar E-mail', 'your-plugin-textdomain' ),
        'all_items'          => __( 'E-mails', 'your-plugin-textdomain' ),
        'search_items'       => __( 'Pesquisar E-mail', 'your-plugin-textdomain' ),
        'parent_item_colon'  => __( 'Parent E-mail:', 'your-plugin-textdomain' ),
        'not_found'          => __( 'No e-mail found.', 'your-plugin-textdomain' ),
        'not_found_in_trash' => __( 'No e-mail found in Trash.', 'your-plugin-textdomain' )
    );

    $args = array(
        'labels'             => $labels,
                'description'        => __( 'Description.', 'your-plugin-textdomain' ),
        'public'             => false,
        'publicly_queryable' => false,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array( 'slug' => 'newsletter' ),
        'capability_type'    => 'post',
        'has_archive'        => false,
        'hierarchical'       => false,
        'menu_position'      => null,
        'supports'           => array( 'title' )
    );

    register_post_type( 'antid_newsletter', $args );
        //add_theme_support( 'post-thumbnails', array( 'antid_area' ) );
}

add_filter('manage_posts_columns' , 'antid_newsletter_columns',10,2);
function antid_newsletter_columns($columns,$post_type='') {
    if ( $post_type == 'antid_newsletter' ){
        $old_date = $columns['date'];
        unset($columns['author']);
        unset($columns['comments']);
        unset($columns['date']);
        $columns['title'] = 'Nome';
        $columns['email'] = 'E-mail';
        $columns['exportado'] = 'Exportado';
        $columns['date'] = $old_date;
    }
    return $columns;
}

function antid_newsletter_short( $atts ){
    if ( count($_POST) > 0 ){
        $arr_post_new = array(
        'post_title' => $_POST['email'],
                'post_type' => 'antid_newsletter',
                'post_status' => 'publish'
    );
        $valid = (bool)preg_match("`^[a-z0-9!#$%&'*+\/=?^_\`{|}~-]+(?:\.[a-z0-9!#$%&'*+\/=?^_\`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$`i", trim($e));
        
        $args = array(
            'post_type' => 'antid_newsletter',
            'meta_key' => 'antid_newsletter_email',
            'meta_value' => $_POST['email']
        );
        $query_exists = new WP_Query($args);
        $already_exists = true;
        if( ! $query_exists->have_posts() ) {
            $_post_id = wp_insert_post($arr_post_new);
            add_post_meta( $_post_id, 'antid_newsletter_email', $_POST['email'] );
            add_post_meta( $_post_id, 'antid_newsletter_exportado', '' );
            $already_exists = false;
            $valid = true;
        }
        
        
        
    }
    $invalid_msg = 'E-mail inválido.';
    if ( $already_exists ){
        $invalid_msg = 'E-mail já existe.';
    }
    $valid_msg = 'E-mail cadastrado com sucesso.';
    return $valid ? $valid_msg : $invalid_msg;
}
add_shortcode( 'antid_newsletter', 'antid_newsletter_short' );




add_filter( 'manage_edit-antid_newsletter_sortable_columns', 'antid_newsletter_sortable_columns' );
function antid_newsletter_sortable_columns( $sortable_columns ) {
   $sortable_columns[ 'email' ] = 'email';
   return $sortable_columns;
}

add_action( 'pre_get_posts', 'antid_newsletter_orderby_email', 1 );
function antid_newsletter_orderby_email( $query ) {

   /**
    * We only want our code to run in the main WP query
    * AND if an orderby query variable is designated.
    */
   if ( $query->is_main_query() && ( $orderby = $query->get( 'orderby' ) ) ) {

      switch( $orderby ) {

         // If we're ordering by 'film_rating'
         case 'email':

            // set our query's meta_key, which is used for custom fields
            $query->set( 'meta_key', 'antid_newsletter_email' );
                
            /**
             * Tell the query to order by our custom field/meta_key's
             * value, in this film rating's case: PG, PG-13, R, etc.
             *
             * If your meta value are numbers, change 'meta_value'
             * to 'meta_value_num'.
             */
            $query->set( 'orderby', 'meta_value' );
                
            break;

      }

   }

}


if( function_exists('register_field_group') ):

register_field_group(array (
        'id' => 'acf_newsletter_fields',
        'title' => 'E-mail',
        'fields' => array (
            array (
                'key'   => 'antid_newsletter_email_1',
                'label' => '',
                'name' => 'antid_newsletter_email',
                'type' => 'text'
            )
        ),
        'location' => array (
            array (
                array (
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'antid_newsletter',
                    'order_no' => 0,
                    'group_no' => 0,
                ),
            ),
        ),
        'options' => array (
            'position' => 'acf_after_title',
            'layout' => 'default',
            'hide_on_screen' => array (
            ),
        ),
        'menu_order' => 0,
    ));

endif;



add_action('manage_posts_custom_column', 'antid_newsletter_email_columns_content', 10, 2);

function antid_newsletter_email_columns_content( $column_name, $post_ID ){
    if ($column_name == 'email') {
        $content = get_field( 'antid_newsletter_email', $post_ID );
        echo $content ? $content : ' - ' ;
    } else if ( $column_name == 'exportado' ){
        $content = get_post_meta($post_ID, 'antid_newsletter_exportado',true);
        echo $content ? "Sim" : "Não" ;
    }
}

add_action('admin_footer-edit.php', 'custom_bulk_admin_footer');
 
function custom_bulk_admin_footer() {
 
  global $post_type;
 
  if($post_type == 'antid_newsletter') {
    ?>
    <script type="text/javascript">
      jQuery(document).ready(function() {
        jQuery('<option>').val('export-all').text('<?php _e('Exportar Todos')?>').appendTo("select[name='action']");
        jQuery('<option>').val('export-all').text('<?php _e('Export Todos')?>').appendTo("select[name='action2']");
        
        jQuery('<option>').val('export').text('<?php _e('Exportar Marcados')?>').appendTo("select[name='action']");
        jQuery('<option>').val('export').text('<?php _e('Export Marcados')?>').appendTo("select[name='action2']");
        
        jQuery('<option>').val('export-nex').text('<?php _e('Exportar Todos Não Exportados')?>').appendTo("select[name='action']");
        jQuery('<option>').val('export-nex').text('<?php _e('Exportar Todos Não Exportados')?>').appendTo("select[name='action2']");
      });
    </script>
    <?php
  }
}


function antid_export( $type ){
    $txtText = '';
    $_qtde = 0;
    switch( $type ) {
        case 'normal':
            $_posts = array();
            if (is_array($_GET) && isset($_GET['post']) && is_array($_GET['post'])){
                $_posts = array_map( 'absint', $_GET['post'] );
                foreach( $_posts as $_post_ID ){
                    $mail = get_post_meta( $_post_ID, 'antid_newsletter_email', true );
                    if ( "" != $mail ){
                        $txtText .= $mail . "\r\n";
                        update_post_meta($_post_ID, 'antid_newsletter_exportado', '1');
                        $_qtde++;
                    }
                }
            }
            break;
        case 'all':
            $args = array(
					'posts_per_page' => -1,
                    'post_type'  => 'antid_newsletter',
                    'meta_key'   => 'antid_newsletter_exportado'
            );
            $query = new WP_Query( $args );
            if ($query->have_posts()){
                while ($query->have_posts()){
                    $query->the_post();
                    global $post;
                    if (isset($post) && is_object($post) && isset($post->ID) ){
                        $post_ID = $post->ID;
                        //the_title();
                        $mail = get_post_meta($post_ID,'antid_newsletter_email',true);
                        $txtText .= $mail . "\r\n";
                        update_post_meta($post_ID, 'antid_newsletter_exportado', '1');
                        $_qtde++;
                    }
                }
            }
            break;
        case 'nex':
            $args = array(
					'posts_per_page' => -1,
                    'post_type'  => 'antid_newsletter',
                    'meta_key'   => 'antid_newsletter_exportado',
                    'meta_query' => array(
                            array(
                                    'key'     => 'antid_newsletter_exportado',
                                    'value'   => '',
                                    'compare' => '=',
                            ),
                    ),
            );
            $query = new WP_Query( $args );
            if ($query->have_posts()){
                while ($query->have_posts()){
                    $query->the_post();
                    global $post;
                    if (isset($post) && is_object($post) && isset($post->ID) ){
                        $post_ID = $post->ID;
                        $mail = get_post_meta($post_ID,'antid_newsletter_email',true);
                        $txtText .= $mail . "\r\n";
                        update_post_meta($post_ID, 'antid_newsletter_exportado', '1');
                        $_qtde++;
                    }
                }
            }
            break;
    }
    
    if ($txtText != ''){
        $txtText = 'email'."\r\n".$txtText;
        $myfile = fopen("newsletter_save.txt", "w");
        fwrite($myfile, $txtText);
        fclose($myfile);
        
    }    
    return $_qtde;
}




add_action('load-edit.php', 'custom_bulk_action');
function custom_bulk_action() {
  $wp_list_table = _get_list_table('WP_Posts_List_Table');
  $action = $wp_list_table->current_action();
  $sendback = "";
    if( isset( $_GET ) && is_array( $_GET ) && !empty( $_GET ) ){
        if( isset( $_GET["post_type"] ) && !empty( $_GET["post_type"] ) ){
           $paged = "";
           $post_type = $_GET['post_type'];
           if( isset( $_GET["paged"] ) && !empty( $_GET["paged"] ) ){
               $paged = $_GET["paged"];
           }
           $sendback = admin_url( 'edit.php?post_type='.$post_type );
           if ( !empty( $paged ) ){
               $sendback .= '&paged='.$paged;
           }
        }
    }
    switch($action) {
        case 'export': {
            $exported = antid_export('normal');
            $sendback = add_query_arg( array('antid_exported' => $exported, 'antid_type_exported' => 'normal'  ), $sendback );
            break;
        }
        case 'export-all': {
            $exported = antid_export('all');
            $sendback = add_query_arg( array('antid_exported' => $exported, 'antid_type_exported' => 'all'  ), $sendback );
            break;
        }
        case 'export-nex': {
            $exported = antid_export('nex');
            $sendback = add_query_arg( array('antid_exported' => $exported, 'antid_type_exported' => 'nex'  ), $sendback );
            break;
        }
        default: {
            $sendback = '';
            break;
        }
    }
    if( isset( $sendback ) && !empty( $sendback ) ){
        wp_redirect( $sendback );
        exit();
    }
}

add_action( 'wp_loaded', 'antid_newsletter_download' );

function antid_newsletter_download(){
    if (  is_array($_GET) && isset( $_GET['post_type'] ) && "antid_newsletter" == $_GET['post_type'] && isset($_GET['download']) ){
        header('Content-disposition: attachment; filename=newsletter.txt');
        header('Content-type: text/plain');
        $myfile = fopen("newsletter_save.txt", "rb");
        fpassthru($myfile);
        fclose($myfile);
        //echo $text;
        exit();
    }
}
function antid_newsletter_footer(){
    if (  is_array( $_GET ) && isset( $_GET['post_type'] ) && "antid_newsletter" == $_GET['post_type'] && isset( $_GET['antid_exported'] ) && !isset( $_GET['download'] ) ){
        $post_type = $_GET['post_type'];
        $paged = $_GET['paged'];
        if ($paged == ''){
            $paged = 1;
        }
        $sendback = admin_url( 'edit.php?post_type='.$post_type );
        if ( "" != $paged ){
            $sendback .= '&paged='.$paged;
        }
        $sendback .= '&download';
        $exported = absint( $_GET['antid_exported'] );
        if ( $exported > 0 ){
            ?><meta http-equiv="refresh" content="0;URL=<?php  echo $sendback;  ?>"><?php
        }
    }
}
add_action('admin_footer','antid_newsletter_footer');

add_filter( 'disable_months_dropdown','disable_month_news', 10, 2 );

function disable_month_news($disable=false,$post_type=''){
    if ( "antid_newsletter" == $post_type ){
        $disable = true;
    }
    return $disable;
}


add_filter( 'bulk_post_updated_messages', 'antid_message_newsletter_update', 10, 2 );
function antid_message_newsletter_update($messages=array(), $count=0){
    $_SERVER['REQUEST_URI'] = remove_query_arg( array( 'antid_exported','antid_marked','antid_unmarked','antid_type_exported','download' ), $_SERVER['REQUEST_URI'] );
    return $messages;
}


function antid_newsletter_send_message() {
    if ( is_array( $_REQUEST ) && ( isset( $_REQUEST['antid_exported'] ) ) ){
        $_marked = $_REQUEST['antid_marked'];
        $_unmarked = $_REQUEST['antid_unmarked'];
        $_exported = $_REQUEST['antid_exported'];
        $_type_export = $_REQUEST['antid_type_exported'];
        $updated = '';
        $report = '';
        if ( isset( $_exported ) && "" != $_exported ){
             if ($_exported > 0 ){
                $updated = sprintf( _n( "%d e-mail exportado.", "%d e-mails exportados.", $_exported ), number_format_i18n( $_exported ) );
            } else {
                switch( $_type_export ){
                    case "nex":{
                        $report = 'Todos os e-mail já foram exportados.';
                        break;
                    }
                    case "normal":{
                        $report = 'Não foi selecionado nenhum e-mail.';
                        break;
                    }
                    default:{
                        $report = 'Não há e-mails para exportar.';
                        break;
                    }
                }
            }
        }
        if ( "" != $updated ){
    ?>
    <div class="updated notice notice-success is-dismissible">
        <p><?php _e( $updated, 'my-text-domain' ); ?></p>
    </div>
    <?php
        } else if ( "" != $report ){
            ?>
    <div class="error notice notice-fail is-dismissible">
        <p><?php _e( $report, 'my-text-domain' ); ?></p>
    </div>
            <?php
        }
    }
}
add_action( 'admin_notices', 'antid_newsletter_send_message' );
?>
