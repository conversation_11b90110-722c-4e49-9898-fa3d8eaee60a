<?php
/*
 * Plugin Name: AntiWoocommerce - Atualizar preços - Tag
 * Description: Atualizar preços da tag.
 * Version:     0.0.1
 * Author:      Matheus
 */

if ( defined( 'ABSPATH' ) && !class_exists( 'AntiWoocommerce_Update_Prices_Tag' ) ) {
    class AntiWoocommerce_Update_Prices_Tag {
        
        public $index_nonce = 'antid_update_nonce';
        public $content_nonce = 'antid_update_price_tag';
        public $separador = ',';
        public $variations = -1;
        public $main_product = -1;
        public $simple = -1;
        
        
        function enqueue_scripts_admin(){
            $path = __FILE__;
            $path_file = dirname( $path );
            $path_url = plugins_url( '', $path );
            
            // Adiciona script para depuração
            wp_enqueue_script( 'antid-tag-price-debug', $path_url . '/admin/js/debug.js', array( 'jquery', 'select2' ), '1.0', true );
            
            // Certifica-se de que o Select2 está carregado
            wp_enqueue_script( 'select2' );
            wp_enqueue_style( 'select2' );
            
            // Adiciona script para inicializar o campo de seleção de tags
            wp_add_inline_script( 'antid-tag-price-debug', '
                jQuery(document).ready(function($) {
                    console.log("Inicializando campo de tags...");
                    
                    // Inicializa o campo de seleção de tags
                    $("#update_price_field").select2({
                        ajax: {
                            url: ajaxurl,
                            dataType: "json",
                            delay: 250,
                            data: function(params) {
                                return {
                                    term: params.term,
                                    action: "woocommerce_antid_find_tag"
                                };
                            },
                            processResults: function(data) {
                                var options = [];
                                if (data) {
                                    $.each(data, function(id, text) {
                                        options.push({id: id, text: text});
                                    });
                                }
                                return {
                                    results: options
                                };
                            },
                            cache: true
                        },
                        minimumInputLength: 1
                    });
                    
                    console.log("Campo de tags inicializado.");
                });
            ' );
        }
        
        function update_price(){
            if( is_admin() && isset( $_GET ) && is_array( $_GET ) && isset( $_GET["page"] ) && $_GET["page"] == $this->current_pag_id ){
                if( isset( $_POST ) && !empty( $_POST ) ){
                    // Adiciona log para depuração
                    error_log('Método update_price iniciado');
                    
                    $index_nonce = $this->index_nonce;
                    $content_nonce = $this->content_nonce;
                    $separador = $this->separador;
                    $percentage = false;
                    $str_price = "";
                    
                    // Verifica se o nonce é válido
                    if( isset( $_POST[$index_nonce] ) && wp_verify_nonce( $_POST[$index_nonce], $content_nonce ) ){
                        error_log('Nonce verificado com sucesso');
                        
                        // Verifica se há tags selecionadas
                        if( isset( $_POST["parent_id"] ) && !empty($_POST["parent_id"]) ){
                            $parent_id = $_POST["parent_id"];
                            error_log('Tags selecionadas: ' . print_r($parent_id, true));
                            
                            if( is_array( $parent_id ) ){
                                $parent_expl = $parent_id;
                            } else {
                                $parent_expl = explode( $separador, $parent_id );
                            }
                            
                            // Verifica se há um preço definido
                            if( isset( $_POST["_price_change"] ) && !empty($_POST["_price_change"]) ){
                                $price_change = $_POST["_price_change"];
                                error_log('Preço a ser alterado: ' . $price_change);
                                
                                // Verifica se é porcentagem ou valor fixo
                                if( strpos( $price_change, '%' ) === false ){
                                    $str_price = $price_change;
                                    $str_price = wc_format_decimal( $str_price, 2 );
                                    error_log('Usando preço fixo: ' . $str_price);
                                } else {
                                    $percentage = true;
                                    $str_price_nop = str_replace( '%', '', $price_change );
                                    $price_pct = $str_price_nop/100;
                                    
                                    // Determina se é aumento ou desconto
                                    if(strpos($str_price_nop, '-') === 0) {
                                        $str_op = '-';
                                        $price_pct = abs($price_pct);
                                    } else {
                                        $str_op = '+';
                                    }
                                    
                                    $str_price = 'pm.meta_value ' . $str_op . ' ( pm.meta_value * ' . $price_pct . ' )';
                                    error_log('Usando porcentagem: ' . $str_price);
                                }
                                
                                if( count( $parent_expl ) > 0 && !empty($str_price) ){
                                    global $wpdb;
                                    $prefix = $wpdb->prefix;
                                    error_log('Prefixo da tabela: ' . $prefix);
                                    
                                    foreach( $parent_expl as $id_update ){
                                        if( $id_update > 0 ){
                                            $term_obj = get_term( $id_update, "product_tag" );
                                            
                                            if( is_wp_error($term_obj) ) {
                                                error_log('Erro ao obter a tag: ' . $term_obj->get_error_message());
                                                continue;
                                            }
                                            
                                            if( is_object( $term_obj ) ){
                                                if( isset( $term_obj->term_id ) && $term_obj->term_id == $id_update ){
                                                    if( isset( $term_obj->slug ) && !empty($term_obj->slug) ){
                                                        $term_slug = $term_obj->slug;
                                                        error_log('Processando tag: ' . $term_slug);
                                                        
                                                        // SQL para variações de produtos
                                                        $sql_variation = "
                                                        UPDATE {$prefix}postmeta pm
                                                        INNER JOIN {$prefix}posts p ON pm.post_id = p.ID AND ( pm.meta_key = '_price' OR pm.meta_key = '_regular_price' )
                                                        INNER JOIN ( 
                                                            SELECT tr.* FROM {$prefix}term_taxonomy tt
                                                            INNER JOIN {$prefix}terms t ON t.term_id = tt.term_id
                                                            INNER JOIN {$prefix}term_relationships tr ON tr.term_taxonomy_id = tt.term_taxonomy_id
                                                            WHERE taxonomy = 'product_tag' AND slug = '" . esc_sql($term_slug) . "' 
                                                        ) x ON x.object_id = p.post_parent
                                                        SET meta_value = " . $str_price . "
                                                        WHERE p.post_type = 'product_variation' and p.post_parent <> 0";
                                                        
                                                        // SQL para produtos principais (variáveis)
                                                        $sql_main = "
                                                        UPDATE {$prefix}postmeta pm
                                                        INNER JOIN {$prefix}posts p ON pm.post_id = p.ID AND ( pm.meta_key = '_price' ) 
                                                        INNER JOIN ( 
                                                            SELECT tr.* FROM {$prefix}term_taxonomy tt 
                                                            INNER JOIN {$prefix}terms t ON t.term_id = tt.term_id 
                                                            INNER JOIN {$prefix}term_relationships tr ON tr.term_taxonomy_id = tt.term_taxonomy_id 
                                                            WHERE taxonomy = 'product_tag' AND slug = '" . esc_sql($term_slug) . "' 
                                                        ) x ON x.object_id = p.ID 
                                                        INNER JOIN (
                                                            SELECT p.* FROM {$prefix}posts p 
                                                            INNER JOIN ( 
                                                                SELECT tr.object_id, term.slug as product_type 
                                                                FROM {$prefix}term_relationships tr 
                                                                INNER JOIN {$prefix}term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id 
                                                                INNER JOIN {$prefix}terms term ON tt.term_id = term.term_id AND tt.taxonomy = \"product_type\" 
                                                            ) y ON y.object_id = p.ID WHERE y.product_type = \"variable\" 
                                                        ) yz ON yz.ID = p.ID
                                                        SET meta_value = " . $str_price . "
                                                        WHERE p.post_type = 'product' and p.ID <> 0";
                                                        
                                                        // SQL para produtos simples
                                                        $sql_simple = "
                                                        UPDATE {$prefix}postmeta pm
                                                        INNER JOIN {$prefix}posts p ON pm.post_id = p.ID AND ( pm.meta_key = '_price' OR pm.meta_key = '_regular_price' )
                                                        INNER JOIN ( 
                                                            SELECT tr.* FROM {$prefix}term_taxonomy tt
                                                            INNER JOIN {$prefix}terms t ON t.term_id = tt.term_id
                                                            INNER JOIN {$prefix}term_relationships tr ON tr.term_taxonomy_id = tt.term_taxonomy_id
                                                            WHERE taxonomy = 'product_tag' AND slug = '" . esc_sql($term_slug) . "' 
                                                        ) x ON x.object_id = p.ID
                                                        INNER JOIN ( 
                                                            SELECT p.* FROM {$prefix}posts p 
                                                            INNER JOIN ( 
                                                                SELECT tr.object_id, term.slug as product_type 
                                                                FROM {$prefix}term_relationships tr 
                                                                INNER JOIN {$prefix}term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id 
                                                                INNER JOIN {$prefix}terms term ON tt.term_id = term.term_id AND tt.taxonomy = \"product_type\" 
                                                            ) y ON y.object_id = p.ID 
                                                            WHERE y.product_type = \"simple\"
                                                        ) yz ON yz.ID = p.ID
                                                        SET meta_value = " . $str_price . "
                                                        WHERE p.ID <> 0";
                                                        
                                                        // Log das consultas SQL para depuração
                                                        error_log('SQL Variation: ' . $sql_variation);
                                                        error_log('SQL Main: ' . $sql_main);
                                                        error_log('SQL Simple: ' . $sql_simple);
                                                        
                                                        // Executa as consultas e armazena os resultados
                                                        $query = $wpdb->query($sql_variation);
                                                        error_log('Resultado da consulta de variações: ' . ($query !== false ? $query : 'erro'));
                                                        $this->variations = ($query !== false) ? $query : 0;
                                                        
                                                        $query2 = $wpdb->query($sql_main);
                                                        error_log('Resultado da consulta de produtos variáveis: ' . ($query2 !== false ? $query2 : 'erro'));
                                                        $this->main_product = ($query2 !== false) ? $query2 : 0;
                                                        
                                                        $query3 = $wpdb->query($sql_simple);
                                                        error_log('Resultado da consulta de produtos simples: ' . ($query3 !== false ? $query3 : 'erro'));
                                                        $this->simple = ($query3 !== false) ? $query3 : 0;
                                                        
                                                        // Limpa o cache de transients do WooCommerce
                                                        wc_delete_product_transients();
                                                        error_log('Cache do WooCommerce limpo');
                                                        
                                                        // Exibe as consultas SQL para depuração quando WP_DEBUG está ativado
                                                        if (defined('WP_DEBUG') && WP_DEBUG && current_user_can('manage_options')) {
                                                            $this->debug_queries($sql_variation, $sql_main, $sql_simple);
                                                        }
                                                    } else {
                                                        error_log('Slug da tag não encontrado');
                                                    }
                                                } else {
                                                    error_log('ID da tag não corresponde');
                                                }
                                            } else {
                                                error_log('Objeto da tag não é válido');
                                            }
                                        } else {
                                            error_log('ID da tag inválido: ' . $id_update);
                                        }
                                    }
                                } else {
                                    error_log('Nenhuma tag selecionada ou preço vazio');
                                }
                            } else {
                                error_log('Preço não definido');
                            }
                        } else {
                            error_log('Nenhuma tag selecionada');
                        }
                    } else {
                        error_log('Verificação de nonce falhou');
                    }
                } else {
                    error_log('Nenhum dado POST recebido');
                }
            }
        }
        
        // Função para processar o teste de atualização de produto
        function process_test_update() {
            if (isset($_POST['test_update']) && isset($_POST['test_product_id']) && isset($_POST['test_price']) && 
                wp_verify_nonce($_POST['test_price_nonce'], 'test_price_update')) {
                
                $product_id = intval($_POST['test_product_id']);
                $price = $_POST['test_price'];
                
                if ($product_id > 0 && !empty($price)) {
                    $product = wc_get_product($product_id);
                    
                    if ($product) {
                        // Verificar se é porcentagem
                        if (strpos($price, '%') !== false) {
                            $percentage = true;
                            $price_value = str_replace('%', '', $price);
                            $is_negative = (strpos($price_value, '-') === 0);
                            $price_value = abs(floatval($price_value)) / 100;
                            
                            // Para produtos variáveis, precisamos calcular o preço para cada variação
                            if ($product->is_type('variable')) {
                                $variations = $product->get_children();
                                $count = 0;
                                
                                foreach ($variations as $variation_id) {
                                    $variation = wc_get_product($variation_id);
                                    if (!$variation) continue;
                                    
                                    $current_price = $variation->get_regular_price();
                                    if (empty($current_price)) continue;
                                    
                                    if ($is_negative) {
                                        $new_price = $current_price - ($current_price * $price_value);
                                    } else {
                                        $new_price = $current_price + ($current_price * $price_value);
                                    }
                                    
                                    // Garantir que o preço não seja negativo
                                    $new_price = max(0, $new_price);
                                    
                                    update_post_meta($variation_id, '_regular_price', $new_price);
                                    update_post_meta($variation_id, '_price', $new_price);
                                    $count++;
                                }
                                
                                // Limpar cache do produto variável
                                $this->clear_variable_product_cache($product_id);
                                
                                echo '<div class="notice notice-success is-dismissible"><p>Produto variável (ID: ' . $product_id . ') com ' . $count . ' variações atualizado com ' . $price . '</p></div>';
                            } else {
                                // Produto simples
                                $current_price = $product->get_regular_price();
                                if (!empty($current_price)) {
                                    if ($is_negative) {
                                        $new_price = $current_price - ($current_price * $price_value);
                                    } else {
                                        $new_price = $current_price + ($current_price * $price_value);
                                    }
                                    
                                    // Garantir que o preço não seja negativo
                                    $new_price = max(0, $new_price);
                                    
                                    update_post_meta($product_id, '_regular_price', $new_price);
                                    update_post_meta($product_id, '_price', $new_price);
                                    
                                    // Limpar cache do produto
                                    wc_delete_product_transients($product_id);
                                    
                                    echo '<div class="notice notice-success is-dismissible"><p>Produto simples (ID: ' . $product_id . ') atualizado para R$ ' . number_format($new_price, 2, ',', '.') . '</p></div>';
                                } else {
                                    echo '<div class="notice notice-error is-dismissible"><p>Produto (ID: ' . $product_id . ') não tem preço regular definido.</p></div>';
                                }
                            }
                        } else {
                            // Preço fixo
                            $new_price = wc_format_decimal($price);
                            
                            if ($product->is_type('simple')) {
                                update_post_meta($product_id, '_regular_price', $new_price);
                                update_post_meta($product_id, '_price', $new_price);
                                
                                // Limpar cache do produto
                                wc_delete_product_transients($product_id);
                                
                                echo '<div class="notice notice-success is-dismissible"><p>Produto simples (ID: ' . $product_id . ') atualizado para R$ ' . number_format($new_price, 2, ',', '.') . '</p></div>';
                            } elseif ($product->is_type('variable')) {
                                $variations = $product->get_children();
                                $count = 0;
                                
                                foreach ($variations as $variation_id) {
                                    update_post_meta($variation_id, '_regular_price', $new_price);
                                    update_post_meta($variation_id, '_price', $new_price);
                                    $count++;
                                }
                                
                                // Limpar cache do produto variável
                                $this->clear_variable_product_cache($product_id);
                                
                                echo '<div class="notice notice-success is-dismissible"><p>Produto variável (ID: ' . $product_id . ') com ' . $count . ' variações atualizado para R$ ' . number_format($new_price, 2, ',', '.') . '</p></div>';
                            }
                        }
                    } else {
                        echo '<div class="notice notice-error is-dismissible"><p>Produto não encontrado (ID: ' . $product_id . ')</p></div>';
                    }
                } else {
                    echo '<div class="notice notice-error is-dismissible"><p>ID do produto inválido ou preço vazio</p></div>';
                }
            }
        }
        
        function message(){
            $msg = "";
            $qtde_update = 0;
            if( $this->simple > 0 ){
                $simple = $this->simple;
                $simple_qty = $simple / 2;
                if( $simple_qty > intval( $simple_qty ) ){
                    $simple_qty = intval( $simple_qty ) + 1;
                }
                $qtde_update += $simple_qty;
                $msg .= $simple_qty;
                if( $simple_qty > 1 ){
                    $msg .= ' produtos simples';
                } else {
                    $msg .= ' produto simples';
                }
            }
            if( $this->main_product > 0 ){
                if( $msg != "" ){
                    $msg .= " e ";
                }
                $msg .= $this->main_product;
                $qtde_update += $this->main_product;
                if( $this->main_product > 1 ){
                    $msg .= ' produtos variáveis';
                } else {
                    $msg .= ' produto variável';
                }
                
                
                if( $this->variations > 0 ){
                    $variation = $this->variations;
                    $variation_qty = $variation / 2;
                    if( $variation_qty > intval( $variation_qty ) ){
                        $variation_qty = intval( $variation_qty ) + 1;
                    }
                    if( $msg != "" ){
                        $msg .= " com ";
                    }
                    $msg .= $variation_qty;
                    if( $variation_qty > 1 ){
                        $msg .= ' variações de produto';
                    } else {
                        $msg .= ' variação de produto';
                    }
                }
            }
            if( $msg != "" ){
                if( $qtde_update > 0 ){
                    if( $qtde_update > 1 ){
                        $msg .= " atualizados";
                    } else {
                        $msg .= " atualizados";
                    }
                    $msg .= " com sucesso.";
                    ?>
                    <div class="notice notice-success is-dismissible">
                        <p><?php echo $msg; ?></p>
                    </div>
                    <?php
                }
            }
        }
        
        function menu_content(){
            ?>
            <form action="" method="POST" id="update_price">
                <p class="form-field">
                        <label for="parent_id"><?php _e( 'Tags', 'woocommerce' ); ?> <?php echo wc_help_tip( 'Selecione a etiqueta para alterar o preço' ); ?></label>
                        <select class="wc-product-search" style="width: 50%;" id="update_price_field" name="parent_id[]" data-placeholder="<?php echo "Pesquisar tag..." ?>" data-action="woocommerce_antid_find_tag" data-allow_clear="true" multiple data-exclude="">
                        </select>
                        
                </p>
                <?php wp_nonce_field( $this->content_nonce, $this->index_nonce ); ?>
                <small>Digite o preço (Ex: 10,00) ou a porcentagem (10%). Para porcentagem, utilize -10% para abaixar o preço em 10% e apenas 10% para aumentar o preço em 10%</small>
                <label for="parent_id"><?php _e( 'Price', 'woocommerce' ); ?> (R$ ou %) <?php echo wc_help_tip( 'Digite o preço (Ex: 10,00) ou a porcentagem (10%). Para porcentagem, utilize -10% para abaixar o preço em 10% e apenas 10% para aumentar o preço em 10%' ); ?></label>
                <p class="form-field">
                    <?php woocommerce_wp_text_input( array( 'id' => '_price_change', 'label' => '', 'data_type' => 'price' ) ); ?>
                </p>
                <input type="submit" name="submit" value="ENVIAR" class="button button-primary">
                
                <!-- Botão de teste para verificar o JavaScript -->
                <button type="button" id="test-js" class="button">Testar JavaScript</button>
                <div id="js-test-result" style="margin-top: 10px; padding: 10px; background: #f8f8f8; display: none;"></div>
            </form>
            
            <!-- Teste de atualização de produto específico -->
            <div style="margin-top: 20px; padding: 15px; background: #f0f0f0; border: 1px solid #ddd;">
                <h3>Teste de Atualização de Produto</h3>
                <form method="post" action="">
                    <p>
                        <label for="test_product_id">ID do Produto:</label>
                        <input type="number" name="test_product_id" id="test_product_id" value="" min="1" step="1" />
                    </p>
                    <p>
                        <label for="test_price">Novo Preço:</label>
                        <input type="text" name="test_price" id="test_price" value="" placeholder="Ex: 99.90 ou 10%" />
                    </p>
                    <?php wp_nonce_field('test_price_update', 'test_price_nonce'); ?>
                    <p>
                        <input type="submit" name="test_update" value="Testar Atualização" class="button" />
                    </p>
                </form>
            </div>
            
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Teste para verificar se o jQuery está funcionando
                $('#test-js').on('click', function() {
                    $('#js-test-result').show().html('<p>JavaScript está funcionando!</p>');
                    
                    // Verifica se o select2 está inicializado
                    if ($.fn.select2) {
                        $('#js-test-result').append('<p>Select2 está disponível.</p>');
                        
                        // Verifica se o campo de tags está inicializado
                        var tagField = $('#update_price_field');
                        if (tagField.length) {
                            $('#js-test-result').append('<p>Campo de tags encontrado.</p>');
                            
                            // Verifica se o campo tem a classe select2-hidden-accessible
                            if (tagField.hasClass('select2-hidden-accessible')) {
                                $('#js-test-result').append('<p>Select2 está inicializado no campo de tags.</p>');
                            } else {
                                $('#js-test-result').append('<p style="color:red">Select2 NÃO está inicializado no campo de tags!</p>');
                            }
                        } else {
                            $('#js-test-result').append('<p style="color:red">Campo de tags NÃO encontrado!</p>');
                        }
                    } else {
                        $('#js-test-result').append('<p style="color:red">Select2 NÃO está disponível!</p>');
                    }
                    
                    // Verifica se o campo de preço está inicializado
                    var priceField = $('#_price_change');
                    if (priceField.length) {
                        $('#js-test-result').append('<p>Campo de preço encontrado.</p>');
                    } else {
                        $('#js-test-result').append('<p style="color:red">Campo de preço NÃO encontrado!</p>');
                    }
                });
            });
            </script>
            
            <a href="#" class="mpf-need-this" data-mfp-src="#antidesign-popup">X</a>
            <div id="antidesign-popup" class="mfp-hide white-popup">
                <div class="content">
                    <div class="main_content">
                        <div class="product">
                            <p>Fazendo isso você irá alterar o preço de um ou mais produtos das etiquetas selecionadas.</p>
                            <div class="buttons_modal">
                                <a href="#" class="button yes-confirm bClose smallbutton">Continuar</a>
                                <a href="#" class="button no-confirm bClose smallbutton">Cancelar</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="close-btn"></div>
            </div>
            <?php
        }
        
        public $current_pag_id = "antid_update_prices_tag";
        
        function menu_page(){
            add_menu_page(
                "Woocommerce - Atualizar Preços - Tag",
                'Preços - Tag',
                'manage_options',
                $this->current_pag_id,
                array( $this, "menu_content" ),
                '',
                6
            );
        }
        
        function add_screen_page($screen_arr=array()){
            $screen_this = "toplevel_page_".$this->current_pag_id;
            if( !in_array( $screen_this, $screen_arr ) ){
                $screen_arr[] = $screen_this;
            }
            return $screen_arr;
        }
        
        function antid_find_tag(){
            $tags = array();
            $string = '';
            if( isset( $_GET ) && isset( $_GET["term"] ) ){
                $string = $_GET["term"];
            }
            if( $string != "" ){
                $tags_query = get_terms( array( 'taxonomy' => 'product_tag', 'hide_empty' => false, 'name__like' => $string ) );
                foreach( $tags_query as $tag ){
                    if( is_object( $tag ) && isset( $tag->term_id ) && isset( $tag->name ) ){
                        $term_id = $tag->term_id;
                        $tags[$term_id] = $tag->name;
                    }
                }
            }
            wp_send_json( $tags );
        }
        
        public function __construct() {
            add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_scripts_admin' ) );
            add_action( 'admin_menu', array( $this, "menu_page" ) );
            add_filter( 'woocommerce_screen_ids', array( $this, "add_screen_page" ) );
            add_action( 'wp_ajax_woocommerce_antid_find_tag', array( $this, "antid_find_tag" ) );
            add_action( 'admin_init', array( $this, 'update_price' ) );
            add_action( 'admin_init', array( $this, 'process_test_update' ) );
            
            add_action( 'admin_notices', array( $this, 'message' ) );
        }
        
        function debug_queries($sql_variation, $sql_main, $sql_simple) {
            if (current_user_can('manage_options') && defined('WP_DEBUG') && WP_DEBUG) {
                echo '<div style="background:#f8f8f8;padding:10px;margin:10px 0;border:1px solid #ddd;">';
                echo '<h4>Debug SQL Queries:</h4>';
                echo '<pre>' . esc_html($sql_variation) . '</pre>';
                echo '<pre>' . esc_html($sql_main) . '</pre>';
                echo '<pre>' . esc_html($sql_simple) . '</pre>';
                echo '</div>';
            }
        }
    }
    new AntiWoocommerce_Update_Prices_Tag();
}
