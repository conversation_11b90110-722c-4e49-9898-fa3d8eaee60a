jQuery(function(a){({init(){a(document.body).on("click","#woocommerce_correios-integration_cws_update_services_list",this.update_services_list),a(document.body).on("click","#woocommerce_correios-integration_autofill_empty_database",this.empty_database)},update_services_list(){a("#mainform").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),a.ajax({type:"POST",url:ajaxurl,data:{action:"correios_cws_update_services_list",nonce:WCCorreiosIntegrationAdminParams.update_cws_services_nonce},success(b){window.alert(b.data.message),a("#mainform").unblock()}})},empty_database(){const b=WCCorreiosIntegrationAdminParams.i18n_confirm_message,c=window.confirm(b);c&&(a("#mainform").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),a.ajax({type:"POST",url:ajaxurl,data:{action:"correios_autofill_addresses_empty_database",nonce:WCCorreiosIntegrationAdminParams.empty_database_nonce},success(b){window.alert(b.data.message),a("#mainform").unblock()}}))}}).init()});