<?php  

// Excursões
function codex_custom_init() {

  $labels = array(

    'name' => _x('Excursões', 'post type general name'),
    'singular_name' => _x('Excursões', 'post type singular name'),
    'add_new' => _x('Adicionar Nova', 'excursoes'),
    'add_new_item' => __('Adicionar Nova Excursões'),
    'edit_item' => __('Editar Excursões'),
    'new_item' => __('Nova Excursões'),
    'all_items' => __('Todas as Excursões'),
    'view_item' => __('Ver Excursões'),
    'search_items' => __('procurar Excursões'),
    'not_found' =>  __('Excursão não encontrada'),
    'not_found_in_trash' => __('Nenhuma excurção no Lixo'), 
    'parent_item_colon' => '',
    'menu_name' => 'Excursões'

  );

  $args = array(

    'labels' => $labels,
    'public' => true,
    'publicly_queryable' => true,
    'show_ui' => true, 
    'show_in_menu' => true, 
    'query_var' => true,
    'rewrite' => true,
    'capability_type' => 'post',
    'has_archive' => false, 
    'hierarchical' => false,
    'menu_position' => 5,
    'supports' => array('title','editor','thumbnail')

  ); 

  register_post_type('excursoes',$args);

}

add_action('init', 'codex_custom_init');





function antid_excursao_posts_pre_get( $query = false ){

    if ( is_object( $query ) && isset( $query->query ) ){

        $query_query = $query->query;

        if ( is_array( $query_query ) && isset( $query_query["post_type"] ) && "excursoes" == $query_query["post_type"] && isset( $query_query["antid_organize_excursoes"] ) && true == $query_query["antid_organize_excursoes"] ){

            $query->set( "meta_key", "antid_data" );

        }
        
    }
    
}

add_filter( 'pre_get_posts', 'antid_excursao_posts_pre_get', 10, 2 );






function antid_excursao_posts_where( $where, $query = false ){

    if ( is_object( $query ) && isset( $query->query ) ){

        $query_query = $query->query;

        if ( is_array( $query_query ) && isset( $query_query["post_type"] ) && "excursoes" == $query_query["post_type"] && isset( $query_query["antid_organize_excursoes"] ) && true == $query_query["antid_organize_excursoes"] ){

            $where .= " AND ( wp_postmeta.meta_value IS NOT NULL AND wp_postmeta.meta_value <> '' AND CURRENT_DATE <= STR_TO_DATE(wp_postmeta.meta_value,'%Y%m%d') ) ";

        }
        
    }
    
    return $where;

}

add_filter( 'posts_where', 'antid_excursao_posts_where', 10, 2 );




function antid_excursao_posts_orderby( $orderby, $query = false ){

    if ( is_object( $query ) && isset( $query->query ) ){

        $query_query = $query->query;

        if ( is_array( $query_query ) && isset( $query_query["post_type"] ) && "excursoes" == $query_query["post_type"] && isset( $query_query["antid_organize_excursoes"] ) && true == $query_query["antid_organize_excursoes"] ){

            if( $orderby != "" ){

                $orderby = "," . $orderby;

            }
            
            $orderby = " wp_postmeta.meta_value ASC " . $orderby;

        }
        
    }
    
    return $orderby;

}


// EXCURSÕES
// página excursões --> https://hmrock.com.br/excursoes-americana-sp/
if (!is_page(7393)){


	add_filter( 'wpcf7_special_mail_tags', 'antid_mail_tag', 10, 3 );



	add_shortcode( 'antid_excursao', 'antid_excursao_shortcode' );



	function antid_wpcf7_form_elements( $form ) {

	   $form = do_shortcode( $form );

	   return $form;

	}
	
	add_filter( 'wpcf7_form_elements', 'antid_wpcf7_form_elements' );



	function antid_excursao_shortcode($args=''){

	    $mail = false;

	    if ( is_array( $args ) ){

	        $mail = in_array( "mail", $args );

	    }
	    
	    $content = "";

	    $antid_encomenda_product = '';

	    if (isset($_COOKIE) && isset($_COOKIE['antid_excursao'])){

	        $antid_encomenda_product = $_COOKIE['antid_excursao'];

	        if ( $antid_encomenda_product != "" && $antid_encomenda_product > 0 ){

	            $content = get_the_title( $antid_encomenda_product );

	        }
	        
	    }
	    
	    if (gettype( $antid_encomenda_product ) == 'string' && $antid_encomenda_product != ''){

	        wc_setcookie( 'antid_excursao', '', 1, false );

	    }
	    
	   if ( $mail ){


	   }
	    else {


	   }
	   
	   $output = $content;

	   return $output;

	}
	


	if ( !class_exists( "Antid_Mail_Tags" ) ){

	    class Antid_Mail_Tags {

	        public $tags;

	        public $all_names = array(
	            'antid_excursao',
	            'antid_data_excursao'
	        );


	        function add($string){

	            $_return = true;

	            $tags = $this->tags;

	            if ( !is_array( $tags ) ){

	                $this->tags = array();

	                $tags = $this->tags;

	            }
	            
	            foreach( $tags as $tag ){

	                if( $tag == $string ){

	                    $_return = false;

	                    break;

	                }
	                
	            }
	            
	            if( $_return ){

	                $this->tags[] = $string;

	            }
	            
	            return $_return;

	        }
	        

	        function ended(){

	            $_return = false;

	            $tags = array();

	            if ( isset( $this->tags ) ){

	                $tags = $this->tags;

	                if ( !is_array( $tags ) ){

	                    $tags = array();

	                }
	                 else {

	                    if ( isset( $this->all_names ) ){

	                        $all_names = $this->all_names;

	                        if ( is_array( $all_names ) ){

	                            $setted = 0;

	                            $count = count( $all_names );

	                            foreach( $all_names as $name ){

	                                if ( in_array( $name, $tags ) ){

	                                    $setted++;

	                                }
	                                
	                            }
	                            
	                            if( $setted > 0 && $setted == $count ){

	                                $_return = true;

	                            }
	                            
	                        }
	                        
	                    }
	                    
	                }
	                
	            }
	            
	            return $_return;

	        }
	        

	    }
	    
	    $antid_mail_tags = new Antid_Mail_Tags();

	}
	



	function antid_mail_tag( $output, $name, $html ) {

	    global $antid_mail_tags;

	    if ( isset( $antid_mail_tags ) && is_object( $antid_mail_tags ) ){

	       if ( 'antid_excursao' == $name ){

	            $antid_mail_tags->add( $name );

	            $content = "";

	            $antid_encomenda_product = '';

	            if (isset($_COOKIE) && isset($_COOKIE['antid_excursao'])){

	                $antid_encomenda_product = $_COOKIE['antid_excursao'];

	                if ( $antid_encomenda_product != "" && $antid_encomenda_product > 0 ){

	                   $content = get_the_title( $antid_encomenda_product );

	                }
	                
	            }
	            

	            if( $antid_mail_tags->ended() ){

	                if ( gettype( $antid_encomenda_product ) == 'string' && $antid_encomenda_product != '' ){

	                    wc_setcookie( 'antid_excursao', '', 1, false );

	                }
	                
	            }
	            

	           $output = $content;

	       }
	       
	       else if ( 'antid_data_excursao' == $name ){

	           $antid_mail_tags->add( $name );


	           $content = "";

	           $antid_encomenda_product = '';

	            if ( isset( $_COOKIE ) && isset( $_COOKIE['antid_excursao'] ) ){

	                $antid_encomenda_product = $_COOKIE['antid_excursao'];

	                if ( $antid_encomenda_product != "" && $antid_encomenda_product > 0 ){

	                    $content = get_the_title( $antid_encomenda_product );

	                    $antid_data = get_field( 'antid_data', $antid_encomenda_product );

	                    $antid_data_html = "";

	                    if ( $antid_data != "" ){

	                            $antid_ano = substr($antid_data, 0, 4);

	                            $antid_mes = substr($antid_data, 4, 2);

	                            $antid_dia = substr($antid_data, 6, 2);

	                            if( $antid_dia != "" && $antid_mes != "" && $antid_ano != "" ){

	                                    $antid_data_arr = array ( $antid_dia, $antid_mes, $antid_ano );

	                                    $content = implode( '/', $antid_data_arr );

	                            }
	                            
	                    }
	                    
	                }
	                
	           }
	           

	            if( $antid_mail_tags->ended() ){

	                if ( gettype( $antid_encomenda_product ) == 'string' && $antid_encomenda_product != '' ){

	                    wc_setcookie( 'antid_excursao', '', 1, false );

	                }
	                
	            }
	            

	            $output = $content;

	        }
	        
	    }
	    
	   return $output;

	}
	
	add_action( 'wp_ajax_reserva_excursao', 'antid_reserva_excursao_call' );


	function antid_reserva_excursao_call() {

	    $response = array();

	    $ok = false;

	    $content = "";

	    if ( isset( $_POST['_excursao'] ) ){

	        $___product = $_POST['_excursao'];

	        if ( $___product == "" ){

	            $___product = 0;

	        }
	        
	    
	        setcookie('antid_excursao', $___product, time()+3600*24*100,COOKIEPATH, COOKIE_DOMAIN, false);

	        if ( $___product != 0 && $___product != "" ){

	            $content = get_the_title( $___product );

	            $ok = true;

	        }
	        
	    }
	    

	    $response['ok'] = $ok;

	    $response['title'] = $content;

	    wp_send_json($response);

	    wp_die();
	     // this is required to terminate immediately and return a
	}
	
	add_action( 'wp_ajax_nopriv_reserva_excursao','antid_reserva_excursao_call' );


}


add_filter( 'posts_orderby', 'antid_excursao_posts_orderby', 10, 2 );


function excursoes_script_enqueue() {

    if (is_page(7393)) :
        wp_register_script( 'excursoes_script', get_stylesheet_directory_uri() . "/js/excursoes.js", array( 'jquery' ) );

        wp_localize_script( 'excursoes_script', 'ANTIDESIGN_EXCURSOES_AJAXURL', admin_url( 'admin-ajax.php' ) );

        wp_enqueue_script( 'excursoes_script' );

    endif;

}


add_action( 'wp_enqueue_scripts', 'excursoes_script_enqueue' );
