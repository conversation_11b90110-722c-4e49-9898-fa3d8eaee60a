<?php




/**
 * 
 * VERSÃO ANTIGA
 */
/*

function antid_search_where( $query = false ){

    global $wpdb;

    $antid_post__in = array();

    
    if ( is_search() && is_object( $query ) && $query->is_main_query() && !is_admin() ){

    
        if ( is_object( $query ) && isset( $query->query ) ){

            $antid_query = $query->query;

            $antid_like = "";

            $remove_html = "";

            
            if ( is_array( $antid_query ) && isset( $antid_query["s"] ) && $antid_query["s"] != "" ){

                $antid_like = $antid_query["s"];

                $search_query = "";

                
                if ( is_search() && is_object( $query ) && $query->is_main_query() ){

                    if ( isset( $query->query ) ){

                        $query_new = $query->query;

                        if ( is_array( $query_new ) && isset($query_new['s'] ) ){

                            $search_query = $query_new['s'];

                        }

                    }

                    
                    if( $search_query != "" ){

                        $query_str = "
                        SELECT * FROM wp_posts p JOIN  $wpdb->postmeta as apm ON apm.post_id = p.ID and apm.meta_key='_sku' AND apm.meta_value LIKE '%".$search_query."%' JOIN  $wpdb->postmeta as visibility ON visibility.post_id = p.ID and visibility.meta_key = '_visibility' and visibility.meta_value <> 'hidden' WHERE 1=1 and p.post_status = 'publish' and p.post_type = 'product' ";


                        $matchedProducts = $wpdb->get_results( $query_str );


                        //var_dump($matchedProducts);

                        $antid_post__in = array();

                       
                        if(is_array($matchedProducts) && !empty($matchedProducts)){

                            foreach( $matchedProducts as $product ){

                                if ( is_object( $product ) && isset($product->ID ) ){

                                   $antid_post__in[] = $product->ID;

                                }

                            }

                        }



                        $query_str = "
                           select p.ID as post_id from $wpdb->terms t
                           join $wpdb->term_taxonomy tt
                           on t.term_id = tt.term_id
                           join $wpdb->term_relationships tr
                           on tt.term_taxonomy_id = tr.term_taxonomy_id
                           join $wpdb->posts p
                           on p.ID = tr.object_id
                           join $wpdb->postmeta visibility
                           on p.ID = visibility.post_id
                           and visibility.meta_key = '_visibility'
                           and visibility.meta_value <> 'hidden'
                           ";


                        $query_str .= "
                           WHERE
                           tt.taxonomy = 'product_tag' and
                           t.name LIKE '%$search_query%'
                           and p.post_status = 'publish'
                           and p.post_type = 'product'
                           and (p.post_parent = 0 or p.post_parent is null)
                           group by p.ID
                            ;
                            ";


                        $matchedProducts = $wpdb->get_results($query_str );


                        if(is_array($matchedProducts) && !empty($matchedProducts)){

                            foreach( $matchedProducts as $product ){

                                if ( is_object( $product ) && isset($product->post_id ) ){

                                    $antid_post__in[$product->post_id] = $product->post_id;

                                }

                            }

                        }

                    }

                }

            }

        }

    }

    return $antid_post__in;

}



function search_conly_product_tag_remove_excerpt($where, $query = false) {

    $antid_post__in = antid_search_where( $query );

    $remove_html = "";

    if ( is_search() ){

        $arr_str = "";

        if ( !empty( $antid_post__in ) ){

            if ( !empty( $antid_post__in ) ){

                $arr_str = implode( ',', $antid_post__in );

            }

        }

        if ( $arr_str != "" ){

            $remove_html = "OR wp_posts.ID IN ( " . $arr_str . " )";

        }

        $where = preg_replace("/OR \(wp_posts.post_content\s+LIKE\s*(\'\%[^\%]+\%\')\)/",$remove_html, $where );


        $where = preg_replace("/OR \(post_excerpt\s+LIKE\s*(\'\%[^\%]+\%\')\)/", "", $where );

    }

    return $where;

}

add_filter( 'posts_where', 'search_conly_product_tag_remove_excerpt', 999, 2 );



function  antid_post_clauses_search($pieces, $query = false){

    if ( is_search() ){

        $antid_post__in = antid_search_where( $query );

        $case_orderby = "";

        $query_query = $query->query;

        if ( isset( $query_query['s'] ) ){

            $antid_search = $query_query['s'];

        }

        $arr_str = "";

        if ( !empty( $antid_post__in ) ){

            if ( !empty( $antid_post__in ) ){

                $arr_str = implode( ',', $antid_post__in );

            }

        }

        if ( $arr_str != "" ){

            $remove_html = " OR wp_posts.ID IN ( " . $arr_str . " )";

        }

        if ( $antid_search != "" && !empty( $antid_post__in ) ){

            $case_orderby = "( CASE WHEN (wp_posts.post_title LIKE '%". $antid_search . "%') THEN 1 ELSE CASE WHEN wp_posts.ID IN ( " . $arr_str . " ) THEN 0 END END ) DESC";

        }

        if( $case_orderby != "" ){

            if ( $pieces['orderby'] != "" ){

               $pieces['orderby'] = $case_orderby . ',' . $pieces['orderby'];

            }

        }


    }

    return $pieces;

}

add_filter( 'posts_clauses', 'antid_post_clauses_search', 999, 2 );

*/