<?php 
	include_once('inc/assets.php');
	include_once('inc/theme.php');
	include_once('inc/menu.php');
	include_once('inc/cpt.php');
	include_once('inc/banda.php');
	include_once('inc/excursao.php');
	include_once('inc/action.php');
	include_once('inc/search.php');
	include_once('inc/woocommerce.php');
    include_once('inc/woocommerce-produtos-meta.php');


/**
 * Desabilita o atributo "sizes" no tag img
 * referencia: https://make.wordpress.org/core/2024/10/18/auto-sizes-for-lazy-loaded-images-in-wordpress-6-7/
 */	
add_filter('wp_img_tag_add_auto_sizes', '__return_false');


/*
  Desabilita a função ajax para carrinho widget
  https://hmrock/?wc-ajax=get_refreshed_fragments
  removendo o script que é carregado no footer
*/
function de_script() {
    wp_dequeue_script( 'wc-cart-fragments' );

    return true;
}
add_action( 'wp_print_scripts', 'de_script', 100 );


/**
 * Change Action Scheduler default purge to 1 week
 */
function wpb_action_scheduler_purge() {
 return WEEK_IN_SECONDS;
}
add_filter( 'action_scheduler_retention_period', 'wpb_action_scheduler_purge' );


add_action('woocommerce_order_status_changed', 'send_cancelled_email_notifications', 10, 4 );
function send_cancelled_email_notifications( $order_id, $old_status, $new_status, $order ){
    if ( $new_status == 'cancelled' || $new_status == 'failed' ){
        $wc_emails = WC()->mailer()->get_emails(); // Get all WC_emails objects instances
        $customer_email = $order->get_billing_email(); // Get the customer email
    }
 
    if ( $new_status == 'cancelled' ) {
        // change the recipient of the instance
        $wc_emails['WC_Email_Cancelled_Order']->recipient = $customer_email;
        // Sending the email from this instance
        $wc_emails['WC_Email_Cancelled_Order']->trigger( $order_id );
    } 
    elseif ( $new_status == 'failed' ) {
        // change the recipient of the instance
        $wc_emails['WC_Email_Failed_Order']->recipient = $customer_email;
        // Sending the email from this instance
        $wc_emails['WC_Email_Failed_Order']->trigger( $order_id );
    } 
}

//remove OS products from related products in WooCommerce, because they are OOS! by Robin Scott of silicondales.com - see more at https://silicondales.com/tutorials/woocommerce/remove-out-of-stock-products-from-woocommerce-related-products/
function exclude_oos_related_products( $related_posts, $product_id, $args ){
    $out_of_stock_product_ids = (array) wc_get_products( array(
          'status'       => 'publish',
          'limit'        => -1,
          'stock_status' => 'outofstock',
          'return'       => 'ids',
      ) );

      
     $exclude_ids = $out_of_stock_product_ids;
      
    shuffle( $related_posts );

    return array_diff( $related_posts, $exclude_ids );
}
add_filter( 'woocommerce_related_products', 'exclude_oos_related_products', 10, 3 );



// Remove as estrelas de avaliação na grade de produtos
remove_action('woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 5);



/**
 * Altera o texto "Adicionar ao carrinho" para "Comprar" no loop de produtos.
 */
add_filter( 'woocommerce_product_add_to_cart_text', 'texto_botao_comprar_loop', 10, 2 );

function texto_botao_comprar_loop( $texto, $produto ) {
    if( $produto->is_purchasable() && $produto->is_in_stock() ) {
        return 'Comprar';
    }
    return $texto;
}




?>