<?php
/**
 * The template for displaying the homepage.
 *
 * This page template will display any functions hooked into the `homepage` action.
 * By default this includes a variety of product displays and the page content itself. To change the order or toggle these components
 * use the Homepage Control plugin.
 * https://wordpress.org/plugins/homepage-control/
 *
 * Template name: Homepage
 *
 * @package storefront
 */

get_header(); ?>

	<div id="primary" class="content-fullwidth">
		<main id="main" class="site-main" role="main">
			<?php do_action('antid_banner_home');?>


			<div class="home-novidades">
				<div class="home-institucional">
				<div class="horarios">
					<div class="inner">
						seg a sex,<br>das 9h as 18h <Br><Br>
						e sábados<br>das 9h as 15h
					</div>
				</div>
					<?php do_action("menu_institucional"); ?>
				</div>
			
			
			<div class="home-novidades-titulo">
				<div class="verticalline">
					<h2>O QUE HÁ<br>DE NOVO</h2>
				</div>
			</div>
			<!-- RECENTES POR CATEGORIA -->
				
			<section class="storefront-product-section storefront-recent-products novidade">
				
			
			<div class="woocommerce columns-4">
			<ul class="products">
    <?php
        $args = array( 
			'post_type' => 'product', 
			'posts_per_page' => 4, 
			'product_cat' => 'cds', 
			'order' => 'asc',
			'tax_query' => array(
                array(
                    'taxonomy' => 'product_visibility',
                    'field'    => 'slug',
                    'terms'    => 'featured',
                )
            )
        );
        $loop = new WP_Query( $args );
        while ( $loop->have_posts() ) : $loop->the_post(); global $product; ?>

                                <li class="product">  
                    <a href="<?php echo get_permalink( $loop->post->ID ) ?>" title="<?php echo esc_attr($loop->post->post_title ? $loop->post->post_title : $loop->post->ID); ?>">
                                
				<div class="outher">
						
				<?php if (has_post_thumbnail( $loop->post->ID )) echo get_the_post_thumbnail($loop->post->ID, 'shop_catalog'); else echo '<img src="'.woocommerce_placeholder_img_src().'" alt="Placeholder" />'; ?>
				<h3><?php the_title(); ?><?php if ( $price_html = $product->get_price_html() ) : ?>
	<span class="price"><?php echo $price_html; ?></span>
	<?php // do_action( 'woocommerce_antid_product_desconto'); ?>

<?php endif; ?>
</h3>
						  
				
				
				<?php woocommerce_show_product_sale_flash( $post, $product ); ?>
				</div>
					</a>
            </li>

            
            
            
    <?php endwhile; ?>
    <?php wp_reset_query(); ?>
</ul>
			<ul class="products">
    <?php
        $args = array( 
			'post_type' => 'product', 
			'posts_per_page' => 4, 
			'product_cat' => 'vestuarios', 
			'order' => 'asc',
			'tax_query' => array(
                array(
                    'taxonomy' => 'product_visibility',
                    'field'    => 'slug',
                    'terms'    => 'featured',
                )
            )
        );
        $loop = new WP_Query( $args );
        while ( $loop->have_posts() ) : $loop->the_post(); global $product; ?>

                <li class="product">  
                    <a href="<?php echo get_permalink( $loop->post->ID ) ?>" title="<?php echo esc_attr($loop->post->post_title ? $loop->post->post_title : $loop->post->ID); ?>">
                                
				<div class="outher">
						
				<?php if (has_post_thumbnail( $loop->post->ID )) echo get_the_post_thumbnail($loop->post->ID, 'shop_catalog'); else echo '<img src="'.woocommerce_placeholder_img_src().'" alt="Placeholder" />'; ?>
				<h3><?php the_title(); ?><?php if ( $price_html = $product->get_price_html() ) : ?>
	<span class="price"><?php echo $price_html; ?></span>
	<?php // do_action( 'woocommerce_antid_product_desconto'); ?>

<?php endif; ?>
</h3>
						  
				
				<?php woocommerce_show_product_sale_flash( $post, $product ); ?>
				</div>
									</a>
            </li>
            
            
            
    <?php endwhile; ?>
    <?php wp_reset_query(); ?>
</ul>


<ul class="products">
    <?php
        $args = array( 
			'post_type' => 'product', 
			'posts_per_page' => 4, 
			'product_cat' => 'acessorios', 
			'order' => 'asc',
			'tax_query' => array(
                array(
                    'taxonomy' => 'product_visibility',
                    'field'    => 'slug',
                    'terms'    => 'featured',
                )
            )
        );
        $loop = new WP_Query( $args );
        while ( $loop->have_posts() ) : $loop->the_post(); global $product; ?>

                <li class="product">  
                    <a href="<?php echo get_permalink( $loop->post->ID ) ?>" title="<?php echo esc_attr($loop->post->post_title ? $loop->post->post_title : $loop->post->ID); ?>">
                                
				<div class="outher">
						
				<?php if (has_post_thumbnail( $loop->post->ID )) echo get_the_post_thumbnail($loop->post->ID, 'shop_catalog'); else echo '<img src="'.woocommerce_placeholder_img_src().'" alt="Placeholder" />'; ?>
				<h3><?php the_title(); ?><?php if ( $price_html = $product->get_price_html() ) : ?>
	<span class="price"><?php echo $price_html; ?></span>
	<?php // do_action( 'woocommerce_antid_product_desconto'); ?>

<?php endif; ?>
</h3>
						  
				
				<?php woocommerce_show_product_sale_flash( $post, $product ); ?>
				</div>
									</a>
            </li>
            
            
            
    <?php endwhile; ?>
    <?php wp_reset_query(); ?>
</ul>

			</div>
			</section>
			<!--/.products-->

			</div>
			
		</main><!-- #main -->
	</div><!-- #primary -->
<?php get_footer(); ?>
