<?php  

// Remove a barra de administração no frontend
add_filter('show_admin_bar', '__return_false');

// Display 20 products per page.
function new_loop_shop_per_page ($cols) {
    // $ cols contém o número atual de produtos por página com base no valor armazenado em Opções -> Leitura
    // Retorna o número de produtos que você deseja mostrar por página.
    $cols = 20;
    return $cols;
}
add_filter ('loop_shop_per_page', 'new_loop_shop_per_page', 20);


// Define 4 produtos por linha na grade da loja.
if (!function_exists('loop_columns')) {
    function loop_columns() {
        return 4; // 4 products per row
    }
}
add_filter('loop_shop_columns', 'loop_columns', 999);



// altera como os preços são exibidos (ex.: "A partir de...").
function custom_variable_price_html( $price, $product ) {
	    $old_price = $price;
	    $price = '';
        global $woocommerce;
        if( is_object( $woocommerce ) && isset( $woocommerce->version ) && !empty( $woocommerce->version ) ){
            $version = $woocommerce->version;
            if ( version_compare( $version, "3.0", '>=' ) ){
                $modified = false;
                if( is_object( $product ) && method_exists( $product, "get_variation_prices" ) ){
                    $variation_prices = $product->get_variation_prices();
                    if( is_array( $variation_prices ) && isset( $variation_prices["price"] ) && is_array( $variation_prices["price"] ) && !empty( $variation_prices["price"] ) ){
                        $prices = $variation_prices["price"];
                        $min_price = min( $prices );
                        $max_price = max( $prices );
                        if ( ! $min_price || $min_price !== $max_price ) {
                            $price .= '<span class="from">' . __( 'A partir de' ) . ' </span>';
                            $price .= wc_price( $product->get_price() );
                            $modified = true;
                        }
                    }
                }
                if( !$modified ){
                    $price = $old_price;
                }
            } else {
                if ( ! $product->min_variation_price || $product->min_variation_price !== $product->max_variation_price ) {
                    $price .= '<span class="from">' . __( 'A partir de' ) . ' </span>';
                }
                $price .= woocommerce_price( $product->get_price() );
            }
        }
    return $price;
}
add_filter( 'woocommerce_variable_price_html', 'custom_variable_price_html', 10, 2 );


// altera a forma de exibir atributos (em <ul> e <li>).
function custom_woocommerce_attribute( $html, $attribute, $values ) {
    $html = '<ul>';
    foreach ( $values as $value ) {
        $html .= '<li>' . $value . '</li>';
    }
    $html .= '</ul>';
    return $html;
}
add_filter( 'woocommerce_attribute', 'custom_woocommerce_attribute', 10, 3 );




// Modifica a consulta de produtos relacionados para usar a categoria ancestral principal.
function antid_related_args( $args ){
    global $product,$woocommerce;
    $_3_0 = false;
    $product_id = -1;
    if( is_object( $woocommerce ) && isset( $woocommerce->version ) && !empty( $woocommerce->version ) ){
        $version = $woocommerce->version;
        if ( version_compare( $version, "3.0", '>=' ) ){
            $_3_0 = true;
        }
    }
    if( $_3_0 ){
        if ( is_object( $product ) && method_exists( $product, "get_id" ) ){
            $product_id = $product->get_id();
        }
    }
     else {
        if ( is_object( $product ) && isset( $product->id ) ){
            $product_id = $product->id;
        }
    }
    if ( $product_id > 0 ){
       $id = $product_id;
       $terms = get_the_terms( $id, "product_cat" );
       $parent = 0;
       $last_parent = $parent;
        while ( $parent != -1 ){
           $last_parent = $parent;
           $parent = antid_getParent( $parent, $terms );
        }
    }
    if ( $last_parent != 0 && $last_parent != -1 ){
       $args['tax_query'] = array(
            array(
               'taxonomy'  => 'product_cat',
                           'field'     => 'term_id',
               'terms'     => $last_parent
            ),
       );
    }
    unset( $args['post__in'] );
    return $args;
}
add_filter( 'woocommerce_related_products_args', 'antid_related_args' );


/**
 * Essa função está associada a outras funções
 * incluindo a de cima
 */
function antid_getParent( $parent=0, $terms=array() ){
    $return = -1;
    foreach( $terms as $term ){
        if ( is_object( $term ) && isset( $term->parent ) && isset( $term->term_id ) && $term->parent == $parent ){
            $return = $term->term_id;
        }
    }
    return $return;
}



// Produtos relacionados para serem exibidos em ordem aleatória.
function antid_random_orderby_related($args){
    if ( is_array( $args ) && isset( $args["post_type"] ) && "product" == $args["post_type"] ){
        $args['orderby'] = "rand";
    }
    return $args;
}
add_filter( 'woocommerce_related_products_args', 'antid_random_orderby_related', 1002 );


/*
add_filter( 'woocommerce_add_to_cart_validation', 'antid_validate',10,5 );
function antid_validate($validate=true, $product_id=0, $quantity=0, $variation_id=0, $variations=array() ){
    $variable = false;
    $prod_id = $product_id;
    if( $variation_id != 0 ){
        $variable = true;
        $prod_id = $variation_id;
    }
    $product = wc_get_product($prod_id);
    $quantities = WC()->cart->get_cart_item_quantities();
    $qtde_em_pedido = antid_get_stock_qtde($product,$variable);
    $current_qty = isset( $quantities[$prod_id] ) ? $quantities[$prod_id] : 0;
    $stock = $product->get_stock_quantity();
    if ( $qtde_em_pedido > 0 ){
        $stock_old  = $stock - $qtde_em_pedido;
    }
    $qtde_em_pedido = $qtde_em_pedido + $current_qty;
    $quantity_calc = $quantity;
    if ( $quantity_calc > 0 ){
        $quantity_calc = $quantity_calc - 1;
    }
    $qtde_em_pedido = $qtde_em_pedido + $quantity_calc;
    $hasnt_stock = check_stock_pedidos( $product, $variable, $qtde_em_pedido );
    if ( $qtde_em_pedido > 0 ){
        $stock  = $stock - $qtde_em_pedido;
    }
    //echo $stock;
    if ( $hasnt_stock ){
        $validate = false;
        $error = new WP_Error();
        if ( $current_qty > 0 ){
            $error->add( 'n-enough-cart-stock',  sprintf( __( 'You cannot add that amount to the cart &mdash;
             we have %s in stock and you already have %s in your cart.', 'woocommerce' ), $stock_old, $quantity ) );
        } else {
            $error->add( 'out-of-stock', sprintf(__( 'Sorry, we do not have enough "%s" in stock to fulfill your order right now. Please try again in %d minutes or edit your cart and try again. We apologise for any inconvenience caused.', 'woocommerce' ), $product->get_title(), get_option( 'woocommerce_hold_stock_minutes' ) ) );
        }
        //$error->add( 'out-of-stock', sprintf(__( 'Sorry, we do not have enough "%s" in stock to fulfill your order right now. Please try again in %d minutes or edit your cart and try again. We apologise for any inconvenience caused.', 'woocommerce' ), $product->get_title(), get_option( 'woocommerce_hold_stock_minutes' ) ) );
        wc_add_wp_error_notices($error);
    }
    return $validate;
}
    */

/*
add_filter( 'woocommerce_min_password_strength', 'antid_pass_strength' );
function antid_pass_strength($min_pass=''){
    $min_pass = '0';
    return $min_pass;
}
*/
/**
 * Change the strength level on the woocommerce password
 *
 * https://www.webroomtech.com/remove-password-strength-checker-wordpress-woocommerce/
 *
 * Strength Settings
 * 4 = Strong
 * 3 = Medium (default) 
 * 2 = Also Weak but a little stronger 
 * 1 = Password should be at least Weak
 * 0 = Very Weak / Anything
 */


function webroom_change_password_strength( $strength ) {
     return 2;
}
add_filter( 'woocommerce_min_password_strength', 'webroom_change_password_strength' );


/*
function antid_change_discount_price_group( $result, $filter_cart_item, $cart_item_key ){
    if ( $result > 20 ){
        $result = 0;
    }
    return $result;
}
    */



class PrecosCDArchive extends WP_Widget {
    public function __construct(){ parent::__construct( false, $name = 'Preços CDS' ); }
    public function widget($argumentos, $instancia) {
        $current_cat = get_queried_object();
        $term_slug = 'cds';
        if ( is_object( $current_cat ) ){
            if ( isset( $current_cat->taxonomy ) && "product_cat" == $current_cat->taxonomy ){
                if ( isset( $current_cat->parent ) && 0 != $current_cat->parent ){
                    while ( $current_cat->parent != 0 ){
                        $parent = $current_cat->parent;
                        $current_cat = get_term( $parent, "product_cat" );
                    }
                }
                if ( isset( $current_cat->slug ) && $current_cat->slug == $term_slug ){
                    $arr_tags = array(
                            '10-00' => '10,00',
                            '15-00' => '15,00',
                            '17-00' => '17,00',
                            '20-00' => '20,00',
                            '25-00' => '25,00'
                        );
                    echo $argumentos['before_widget'];
                    echo $argumentos['before_title'] . 'Preços' . $argumentos['after_title'];
                    foreach( $arr_tags as $slug_str => $tag ){
                        $tag_obj = get_term_by( 'slug', $slug_str, 'product_tag' );
                        if( is_object( $tag_obj ) ){
                            $tag_class = get_class( $tag_obj );
                            if ( $tag_class != "WP_Error" ){
                                $tag_link = get_term_link( $tag_obj, "product_tag" );
                                if( !is_object( $tag_link ) ){
                                    echo '<a href="'.$tag_link.'" class="botao easy">'.$tag.'</a>';
                                }
                            }
                        }
                    }
                    echo $argumentos['after_widget'];
                }
            }
        }
    }
}
add_action('widgets_init', function() {return register_widget("PrecosCDArchive"); },999);



function antid_fix_related_args($args){
    $args['columns'] = 4;
    return $args;
}
add_filter( 'woocommerce_upsell_display_args', 'antid_fix_related_args', 99999 );


function antid_fix_crosssell_args($columns){
    return 2;
}
add_filter( 'woocommerce_cross_sells_columns', 'antid_fix_crosssell_args', 99999 );


function antid_my_styles_method() {
    $custom_css = "
            .woocommerce-cart.page-template-template-fullwidth-php .cart-collaterals .cross-sells {
                width:100%;
            }
            .woocommerce-cart.page-template-template-fullwidth-php .cart-collaterals .cross-sells ul.products li.product {
                width:18.18%;
            }";
    wp_add_inline_style( 'storefront-child-style', $custom_css );
}
add_action( 'wp_enqueue_scripts', 'antid_my_styles_method', 9999 );


// Essa função altera a query principal para ordenar produtos com estoque primeiro
function antid_orderby_popularidade_produtos_indisponiveis( $args, $query ){
    if ( $query->is_main_query() ){
        $post_type = get_query_var( "post_type", "" );
        $add_to_query = false;
        if( is_product_taxonomy() || is_post_type_archive( "product" ) ){
            $add_to_query = true;
        } else if( is_search() && !empty( $post_type ) && $post_type === "product" ){
            $add_to_query = true;
        }
        if ( ( is_object( $query ) && isset( $query->query_vars ) && is_array( $query->query_vars ) && isset( $query->query_vars["meta_key"] ) && ( "total_sales" == $query->query_vars["meta_key"] || "_price" == $query->query_vars["meta_key"] ) ) || $add_to_query ){
            global $wpdb;
            $args['join'] .= " LEFT JOIN " . $wpdb->postmeta . " AS pmx ON ( {$wpdb->posts}.ID = pmx.post_id AND pmx.meta_key = '_stock_status' ) ";
            $antid_orderby = "( CASE pmx.meta_value WHEN 'instock' THEN 0 ELSE 1 END ) ASC ";
            if ( $args['orderby'] != "" ){
                $args['orderby'] = "," . $args['orderby'];
            }
            $args['orderby'] = $antid_orderby . $args['orderby'];
        }
    }
    return $args;
}

add_filter( 'posts_clauses', 'antid_orderby_popularidade_produtos_indisponiveis', 9999, 2 );



// add_filter( 'woocommerce_shipping_flat_rate_is_available', 'wc_antid_flat_rate_enable', 999999, 2 );
// function wc_antid_flat_rate_enable( $available, $package ){
//     if ( $available ){
//         if( is_object( WC() ) && isset( WC()->cart ) && isset( WC()->cart->cart_contents_total ) && WC()->cart->cart_contents_total < 89 ){
//             $available = false;
//         }
//     }
//     return $available;
// }


// Desativa o script storefront-sticky-payment, que mantém o botão de pagamento fixo na tela no tema Storefront.
function antid_dequeue_sticky_payment() {
   wp_dequeue_script( 'storefront-sticky-payment' );
}
add_action( 'wp_enqueue_scripts', 'antid_dequeue_sticky_payment', 9999 );

// Personaliza a mensagem exibida no checkout referente à aplicação de cupons.
function antidesign_update_coupon_msg($msg){
    $msg = 'Se você possuir um cupom de desconto, <a href="#" class="showcoupon">clique aqui</a> para informar o código.';
    return $msg;
}
add_filter( 'woocommerce_checkout_coupon_message', "antidesign_update_coupon_msg" );

// Remove e re-adiciona o total do carrinho com prioridade menor (mais cedo).
remove_action( 'woocommerce_cart_collaterals', 'woocommerce_cart_totals', 10 );
add_action( 'woocommerce_cart_collaterals', 'woocommerce_cart_totals', 9 );


// Adiciona o código de erro 011 (normalmente “área de risco”) à lista de códigos aceitos pelo plugin WooComemrce Correios
// Evita que a entrega falhe por esse motivo — o plugin tratará a área de risco como válida.
function antidesign_fix_correios_area_risco( $codes = "" ){
    $new_code = "011";
    if( is_array( $codes ) && !in_array( $new_code, $codes ) ){
        array_push( $codes, $new_code );
    }
    return $codes;
}
add_filter('woocommerce_correios_accepted_error_codes', "antidesign_fix_correios_area_risco", PHP_INT_MAX);



# CÓDIGO ABAIXO JÁ ESTAVA COMENTADO
# PORÉM, O QUE ESTAVA COMENTADO ERA O ADD_FILTER, AGORA ESTÁ COMENTADO TODA FUNÇÃO
# COMENTADO DIA 17/10
// Personaliza a mensagem de estoque exibida nos produtos, considerando a quantidade já reservada em pedidos pendentes ou aguardando pagamento.
/*
function change_stock_message($message, $stock_status, $product_x ) {
    if ( ! ( is_object( $product_x ) && isset( $product_x->parent ) && is_object( $product_x->parent ) ) ){
        global $product;
        $variable = false;
    } else {
        $product = $product_x;
        $variable = true;
    }
    $stock_status = $product->stock_status;
    if( $stock_status  == "outofstock" ){
    }else if( check_stock_pedidos( $product, $variable ) ){
        $message = '<p class="stock out-of-stock">Acabou!</p>';
    }else{
        $qtde = antid_get_stock_qtde( $product, $variable );
        if ( $qtde > 0 ){
            $stock_now = $product->get_stock_quantity();
            $stock_full = $stock_now;
            if ( $stock_now > 0 ){
                $stock_full = $stock_full - $qtde;
                if ( $stock_full > 0 ){
                    $stock_html = sprintf( __( '%s in stock', 'woocommerce' ), $stock_full );
                    $message = '<p class="stock in-stock">'.$stock_html.'</p>';
                }
            }
        }
    }
    return $message;
}
//add_filter('woocommerce_stock_html', 'change_stock_message', 10, 3);
*/

/*
function antid_get_stock_qtde( $product, $variable = false ){
    $_product = $product;
    $not_enough_stock = false;
    global $wpdb;
    $order_id = 0;
    $qtde_pedidos = 0;
    $remove_from_payment_method = apply_filters( 'antidesign_remove_payment_method_count_indisponivel', array( 'bacs' ) );
    $where_payment_method = "";
    if( is_array( $remove_from_payment_method ) && count( $remove_from_payment_method ) > 0 ){
        $where_payment_method = " AND ( wp_pm1.meta_value IS NULL OR wp_pm1.meta_value NOT IN ('".implode( "','", $remove_from_payment_method )."') )";
    }
    global $woocommerce;
    if ( $variable ){
        $variation_id = -1;
        $parent_id = -1;
        if( is_object( $woocommerce ) && isset( $woocommerce->version ) && !empty( $woocommerce->version ) ){
            $version = $woocommerce->version;
            if( version_compare( $version, "3.0", '>=' ) ){
                if( is_object( $_product ) && method_exists( $_product, "get_parent_id" ) && method_exists( $_product, "get_id" ) ){
                    $variation_id = $_product->get_id();
                    $parent_id = $_product->get_parent_id();
                }
            } else {
                if( is_object( $_product ) && isset( $_product->parent ) && is_object( $_product->parent ) && isset( $_product->parent->post ) && is_object( $_product->parent->post ) && isset( $_product->parent->post->ID ) ){
                    if (isset( $_product->variation_id ) ){
                        $variation_id = $_product->variation_id;
                        $parent_id = $_product->parent->post->ID;
                    }
                }
            }
        }        
        if( $variation_id > 0 && $parent_id > 0 ){   
            $held_stock = $wpdb->get_var(
                $wpdb->prepare( "
                    SELECT SUM( order_item_meta.meta_value ) AS held_qty
                    FROM {$wpdb->posts} AS posts
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_items as order_items ON posts.ID = order_items.order_id
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta ON order_items.order_item_id = order_item_meta.order_item_id
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta2 ON order_items.order_item_id = order_item_meta2.order_item_id
                                                LEFT JOIN {$wpdb->prefix}postmeta wp_pm1 ON wp_pm1.post_id = posts.ID AND wp_pm1.meta_key = '_payment_method'
                    WHERE     order_item_meta.meta_key   = '_qty'
                    AND     order_item_meta2.meta_key  = %s AND order_item_meta2.meta_value  = %d
                    AND     posts.post_type            IN ( '" . implode( "','", wc_get_order_types() ) . "' )
                    AND     (posts.post_status = 'wc-pending'  OR posts.post_status = 'wc-on-hold' )
                                                " . $where_payment_method . "
                    AND        posts.ID                   != %d;",
                    '_variation_id',
                    $variation_id,
                    $order_id
                )
            );
            $qtde_pedidos = $held_stock;
        }
    }else{
        $product_id = -1;
        if( is_object( $woocommerce ) && isset( $woocommerce->version ) && !empty( $woocommerce->version ) ){
            $version = $woocommerce->version;
            if ( version_compare( $version, "3.0", '>=' ) ){
                if( is_object( $_product ) && method_exists( $_product, "get_id" ) ){
                    $product_id = $_product->get_id();
                }
            }else{
                if(  is_object( $_product ) && isset( $_product->id ) ){
                    $product_id = $_product->id;
                }
            }
        }
        if( $product_id != -1 ){
            $held_stock = $wpdb->get_var(
                $wpdb->prepare( "
                    SELECT SUM( order_item_meta.meta_value ) AS held_qty
                    FROM {$wpdb->posts} AS posts
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_items as order_items ON posts.ID = order_items.order_id
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta ON order_items.order_item_id = order_item_meta.order_item_id
                    LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta as order_item_meta2 ON order_items.order_item_id = order_item_meta2.order_item_id
                    LEFT JOIN {$wpdb->prefix}postmeta wp_pm1 ON wp_pm1.post_id = posts.ID AND wp_pm1.meta_key = '_payment_method'
                    WHERE     order_item_meta.meta_key   = '_qty'
                    AND     order_item_meta2.meta_key  = %s AND order_item_meta2.meta_value  = %d
                    AND     posts.post_type            IN ( '" . implode( "','", wc_get_order_types() ) . "' )
                    AND     (posts.post_status = 'wc-pending'  OR posts.post_status = 'wc-on-hold' )
                    " . $where_payment_method . "
                    AND        posts.ID                   != %d;",
                    '_product_id',
                    $product_id,
                    $order_id
                )
            );
            $qtde_pedidos = $held_stock;
        }
    }
    return $qtde_pedidos;
}
*/


/*
function check_stock_pedidos($product,$variable = false, $check_qty = 1 ){
    $_product = $product;
    $not_enough_stock = false;
    global $wpdb;
    $order_id = 0;
    $qtde_produtos = antid_get_stock_qtde( $product, $variable );
    if ( $_product->get_stock_quantity() < ( $qtde_produtos + $check_qty ) ) {
        $not_enough_stock = true;
    }
    return $not_enough_stock;
}
    */


// Oculta os campos "estado" e "cidade" do calculador de frete na página do carrinho do WooCommerce.
// O usuário  verá apenas o campo de CEP no cálculo de frete, simplificando o formulário.
function hide_shipping_calc_attributes(){
    return false;
}
add_filter( "woocommerce_shipping_calculator_enable_state", "hide_shipping_calc_attributes" );
add_filter( "woocommerce_shipping_calculator_enable_city", "hide_shipping_calc_attributes" );



/*add_filter( "woocommerce_default_address_fields", "antid_default_address_fields" );
function antid_default_address_fields( $fields = "" ){
    if( is_array( $fields ) && isset( $fields["address_2"] ) && is_array( $fields["address_2"] ) && isset( $fields["address_2"]["label_class"] ) && !empty( $fields["address_2"]["label_class"] ) ){
        $classes = array();
        foreach( $fields["address_2"]["label_class"] as $label_class ){
            if( $label_class !== "screen-reader-text" ){
                array_push( $classes, $label_class );
            }
        }
        $fields["address_2"]["label_class"] = $classes;
    }
    return $fields;
}*/


/*
function antidesign_change_stock_message( $availability, $product_x ){
    if( is_object( $product_x ) && method_exists( $product_x, "get_type" ) ){
        $type = $product_x->get_type();
        if( $type === "variable" ){
            $outofstock = check_stock_pedidos_variable_all( $product_x );
            if ( $outofstock ){
                $availability["class"] = "out-of-stock";
                $availability["availability"] = "Acabou!";
            }
        }else{
            $variable = false;
            if( $type == "variation" ){
                $variable = true;
            }
            $outofstock = check_stock_pedidos( $product_x, $variable );
            if ( $outofstock ){
                if( is_array( $availability ) && isset( $availability["class"] ) && $availability["class"] != "out-of-stock" ){
                    $availability["class"] = "out-of-stock";
                    $availability["availability"] = "Acabou!";
                }
            }
        }
    }
    return $availability;
}

add_filter( 'woocommerce_get_availability', "antidesign_change_stock_message", 1, 2 );
*/


/*add_filter( "woocommerce_get_related_product_cat_terms", "wc_antid_related_cat_terms" );
function wc_antid_related_cat_terms( $cats = "", $product_id = "" ){
    $terms = get_the_terms( $product_id, "product_cat" );
    $parent = 0;
    $last_parent = $parent;
    while ( $parent != -1 ){
        $last_parent = $parent;
        $parent = antid_getParent( $parent, $terms );
    }
    if( $last_parent > 0 ){
        $cats = array( $last_parent );
    }
    return $cats;
}
add_filter( "woocommerce_product_related_posts_relate_by_tag", "wc_antid_related_by_tag", 9999, 2 );
function wc_antid_related_by_tag( $bytag = "", $product_id = "" ){
    return false;
}*/
/*add_filter( "woocommerce_product_related_posts_query", function( $query = "" ){
    if ( $outofstock_term = get_term_by( 'name', 'outofstock', 'product_visibility' ) ) {
        if( is_object( $outofstock_term ) && isset( $outofstock_term->term_id ) && !empty( $outofstock_term->term_id ) ){
            $term_id = filter_var( $outofstock_term->term_id, FILTER_VALIDATE_INT );
            if( !empty( $term_id ) && $term_id > 0 ){
                if( is_array( $query ) ){
                    if( !isset( $query["join"] ) ){
                        $query["join"] = "";
                    }
                    $query["join"] .= sprintf( " LEFT JOIN ( SELECT object_id FROM wp_term_relationships WHERE term_taxonomy_id IN ( %d ) ) AS anhmr_exclude_join ON anhmr_exclude_join.object_id = p.ID ", $term_id );
                    $query["join"] .= " LEFT JOIN (
 SELECT anhmr_pm_st.post_id, CASE WHEN anhmr_pm_st.post_id IS NULL THEN 1 ELSE ( CASE WHEN CONVERT( anhmr_pm_st.meta_value,UNSIGNED INTEGER ) > 0 THEN 1 ELSE 0 END  ) END cmp FROM `wp_postmeta` anhmr_pm
LEFT JOIN wp_postmeta anhmr_pm_st ON anhmr_pm_st.post_id = anhmr_pm.post_id AND anhmr_pm_st.meta_key = '_stock'
WHERE anhmr_pm.meta_key = '_manage_stock' AND anhmr_pm.meta_value = 'yes'
) AS anhmr_stock_join ON anhmr_stock_join.post_id = p.ID ";
                    if( !isset( $query["where"] ) ){
                        $query["where"] = "";
                    }
                    $query["where"] .= " AND anhmr_exclude_join.object_id IS NULL ";
                    $query["where"] .= " AND ( anhmr_stock_join.cmp IS NULL OR anhmr_stock_join.cmp > 0 )";
                    $new_query = array();
                    $exists = false;
                    foreach( $query as $query_id => $query_sql ){
                        if( $query_id === "orderby" ){
                           $exists = true;
                           break;
                        }
                    }
                    if( !$exists ){
                        foreach( $query as $query_id => $query_sql ){
                            if( $query_id === "limits" ){
                                $new_query["orderby"] = "";
                            }
                            $new_query[$query_id] = $query_sql;
                        }
                        $query = $new_query;
                    }
                    if( !empty( $query["orderby"] ) ){
                        $query["orderby"] .= ",";
                    } else {
                        $query["orderby"] = " ORDER BY ";
                    }
                    $query["orderby"] .= " RAND() ";
                }
            }
        }
    }
    return $query;
} );
add_theme_support( 'wc-product-gallery-zoom' );
add_theme_support( 'wc-product-gallery-slider' );*/
// add_action( 'manage_shop_order_posts_custom_column' , 'antd_custom_columns', 9999, 2 );
// function antd_custom_columns( $column, $post_id ) {
//     if( $column === "order_total" ){
//         if( !empty( $post_id ) ){
//             $moip_payment_type = get_post_meta( $post_id, "_moip_payment_type", true );
//             if( !empty( $moip_payment_type ) ){
//                 switch( $moip_payment_type ){
//                     case "payBoleto" : {
//                         $str_payment = "Boleto Bancário";
//                         break;
//                     }
//                     case "payCreditCard" : {
//                         $str_payment = "Cartão de Crédito";
//                         break;
//                     }
//                     case "payOnlineBankDebitItau" : {
//                         $str_payment = "Débito Bancário Itaú";
//                         break;
//                     }
//                 }
//                 if( !empty( $str_payment ) ){
//                     echo "<br><small class=\"meta\">Via MOIP(" . $str_payment . ")</small>";
//                 }
//             }
//         }
//     }
// }


// Remove o título padrão dos produtos exibidos no loop da loja
remove_action( 'woocommerce_shop_loop_item_title', 'woocommerce_template_loop_product_title', 10 );

// Remove o menu lateral de navegação da conta do cliente.
remove_action( 'woocommerce_account_navigation', 'woocommerce_account_navigation' );


// Substitui o conteúdo do Dashboard da conta do cliente por uma simulação do endpoint de pedidos,
// passando 1 como parâmetro (possivelmente para paginação ou controle interno).
add_action( "woocommerce_account_dashboard", function(){
    if ( has_action( 'woocommerce_account_orders_endpoint' ) ) {
        do_action( 'woocommerce_account_orders_endpoint', 1 );   
    }
});

/**
 * Modifica os parâmetros da consulta de pedidos no "Minha Conta" quando não estamos diretamente no endpoint orders.
 * Adiciona flags personalizadas (antd_add_total, antd_add_recent_order_title, antd_break_page) para serem interpretadas posteriormente.
 */
add_filter( "woocommerce_my_account_my_orders_query", function( $args = "" ){
    if( !is_wc_endpoint_url( 'orders' ) ){
        $args["limit"] = 15;
        $args["paginate"] = false;
        $args["antd_add_total"] = true;
        $args["antd_add_recent_order_title"] = true;
        $args["antd_break_page"] = true;
    }
    return $args;
} );

// Interpreta as flags adicionadas anteriormente.
add_filter( "woocommerce_order_query", function( $results = "", $args = "" ){
    if( is_array( $args ) && isset( $args["antd_add_total"] ) && $args["antd_add_total"] && isset( $args["paginate"] ) && !$args["paginate"] ){
        if( is_array( $results ) ){
            $new_results = new stdClass();
            $new_results->orders = $results;
            $new_results->total = strval( count( $results ) );
            $new_results->max_num_pages = floatval( 1 );
            $new_results->add_recent_order_title = false;
            $new_results->antd_break_page = false;
            if( count( $results ) > 0 ){
                if( isset( $args["antd_add_recent_order_title"] ) && $args["antd_add_recent_order_title"] ){
                    $new_results->add_recent_order_title = true;
                }
            }else{
                if( isset( $args["antd_break_page"] ) && $args["antd_break_page"] ){
                    $new_results->antd_break_page = true;
                }
            }
            return $new_results;
        }
    }
    return $results;
}, 999, 2 );


/**
 * Adiciona uma coluna com o rótulo "E-mail" no painel administrativo de pedidos,
 * após a coluna "order_date", se a coluna customer_email ainda não estiver presente.
 * Mas não traz o email, precisa da função abaixo
 */
/*
add_filter( "manage_edit-shop_order_columns", function( $columns = "" ){
    $columns_new = array();
    if( is_array( $columns ) && !empty( $columns ) ){
        foreach( $columns as $key => $value ){
            $columns_new[$key] = $value;
            if( $key === "order_date" ){
                if( !isset( $columns["customer_email"] ) ){
                    $columns_new["anhmr_customer_email"] = "E-mail";
                }
            }
        }
        $columns = $columns_new;
    }
    return $columns;
} );
 */


/* DESATIVADA - 20/08 - Nova função abaixo.
/**
 * Apresenta o email do cliente na coluna "anhmr_customer_email" do painel administrativo de pedidos.
 */
/*
add_action( "manage_shop_order_posts_custom_column", function( $column = "", $post_ID = -1 ){
    if( $column === "anhmr_customer_email" ){
        $order = wc_get_order( $post_ID );
        if( method_exists( $order, "get_user" ) ){
            $user = $order->get_user();
            if( is_object( $user ) && isset( $user->user_email ) && !empty( $user->user_email ) ){
                $email = $user->user_email;
                echo '<a href="mailto:' . $email . '">' . $email . '</a>';
            }
        }
    }    
}, 10, 2 );
*/

/*
add_action( "manage_shop_order_posts_custom_column", function( $column = "", $post_ID = -1 ) {
    if( $column === "anhmr_customer_email" ){
        $order = wc_get_order( $post_ID );
        // Verifica se $order é um objeto antes de prosseguir
        if( is_object( $order ) && method_exists( $order, "get_user" ) ){
            $user = $order->get_user();
            if( is_object( $user ) && isset( $user->user_email ) && !empty( $user->user_email ) ){
                $email = $user->user_email;
                echo '<a href="mailto:' . $email . '">' . $email . '</a>';
            }
        }
    }    
}, 10, 2 );
*/

/**
 * garante que o país de envio (shipping_country) de um cliente sempre seja válido, de acordo com os países habilitados em WooCommerce.
 */
add_filter( "woocommerce_customer_get_shipping_country", function( $value = "", $customer = "" ){
    if( function_exists( "WC" ) ){
        $wc = WC();
        if( is_object( $wc ) && isset( $wc->countries ) && is_object( $wc->countries ) && method_exists( $wc->countries, "get_shipping_countries" ) ){
            $countries_enabled = $wc->countries->get_shipping_countries();
            if( empty( $value ) ){
                if( is_array( $countries_enabled ) && !empty( $countries_enabled ) ){
                    foreach( $countries_enabled as $key => $value_now ){
                        if( !empty( $key ) ){
                            $value = $key;
                            break;
                        }
                    }
                }
            } else {
                if( is_array( $countries_enabled ) && !empty( $countries_enabled ) ){
                    $found = false;
                    $first_country = "";
                    foreach( $countries_enabled as $key => $value_now ){
                        if( !empty( $key ) && empty( $first_country ) ){
                            $first_country = $key;
                        }
                        if( !empty( $key ) && $key == $value ){
                            $found = true;
                            break;
                        }
                    }
                    if( !$found ){
                        $value = $first_country;
                    }
                }
            }
        }
    }
    return $value;
}, 10, 2 );


/**
 * Permite que pedidos com status cancelled sejam refeitos (funcionalidade "Pedir novamente").
 */
add_filter( "woocommerce_valid_order_statuses_for_order_again", function( $order_types = "" ){
    if( is_array( $order_types ) && !empty( $order_types ) && !in_array( "cancelled", $order_types ) ){
        array_push( $order_types, "cancelled" );
    }
    return $order_types;
} );


/******
 * 
 * adicionar classes CSS adicionais ao elemento <body> de páginas
 * de categorias de produtos ou de produtos individuais
 * 
 */
function browser_body_class($classes = '') {
      if (is_product_category()){
          $current_category = get_queried_object();
          $parent__ = $current_category->parent;
          if ($parent__ <> 0){
              $current_category = get_term($current_category->parent,'product_cat');
          }
          if ($current_category->slug != ""){
              $classes[] = ''.$current_category->slug;
          }
      } else if (is_product()){
          $post = get_queried_object();
          if( is_object( $post ) && isset( $post->ID ) ){
              $terms = get_the_terms( $post->ID, 'product_cat' );
                foreach ($terms as $term) {
                    $product_cat_id = $term->term_id;
                    $___parent = $term->parent;
                    if ($term->parent == 0){
                        $classes[] = ''.$term->slug;
                    }
                }
          }
      }
  return $classes;
}
add_filter('body_class','browser_body_class');


//Reposition WooCommerce breadcrumb 
function jk_remove_storefront_breadcrumb() {
    remove_action( 'storefront_content_top', 'woocommerce_breadcrumb',  20 );
}
add_action( 'init', 'jk_remove_storefront_breadcrumb' );

/**
 * Encapsula o breadcrumb do WooCommerce em uma action customizada.
 * Chamar no front: do_action( 'woo_custom_breadcrumb' ); 
 */
function woocommerce_custom_breadcrumb(){
    woocommerce_breadcrumb();
}
add_action( 'woo_custom_breadcrumb', 'woocommerce_custom_breadcrumb', 20 );


// Customize Woocommerce Related Products Output
// Display 4 products in 4 columns
function woocommerce_output_related_products() {
    woocommerce_related_products( array( "posts_per_page" => 4, "columns" => 4 ) );   
}


// Remove abas da página de produtos
function woo_remove_product_tabs( $tabs ) {
    //unset( $tabs['description'] );        // Remove the description tab
    //unset( $tabs['reviews'] );            // Remove the reviews tab
    unset( $tabs['additional_information'] );   // Remove the additional information tab
    return $tabs;
}
add_filter( 'woocommerce_product_tabs', 'woo_remove_product_tabs', 98 );



/**
 * Ele só terá efeito se estiver implementado por outro plugin ou função do seu tema/sistema.
 * Caso contrário, o código não será executado.
 */
/*
function antidesign_indisponiveis_payment_method_ignore( $methods ){
    $arr_add = array( "cod", "cheque", "woo-moip-official" );
    foreach( $arr_add as $method ){
        if( is_array( $methods ) && !in_array( $method, $methods ) ){
            array_push( $methods, $method );
        }
    }
    return $methods;
}
add_filter( "antidesign_remove_payment_method_count_indisponivel", "antidesign_indisponiveis_payment_method_ignore" );
*/

/**
* Este filtro personalizado controla a exibição de variações
* esgotadas de um produto no WooCommerce, provavelmente em um contexto customizado.
 */
add_filter( "antidesign_display_variable_outofstock", function( $display = false, $product = "" ){
    if( !$display ){
        if( is_object( $product ) ){
            $outofstock = check_stock_pedidos( $product, true );
            if ( $outofstock ){
                $display = true;
            }
        }
    }
    return $display; 
}, 10, 2 );

/**
 * Controlar a exibição de produtos fora de estoque no front-end (em listagens, por exemplo), de forma customizada.
 */
add_filter( "antidesign_display_outofstock", function( $display = false, $product = "" ){
    if( !$display ){
        if( is_object( $product ) ){
            $outofstock = check_stock_pedidos( $product, false );
            if ( $outofstock ){
                $display = true;
            }
        }
    }
    return $display;
}, 10, 2 );

/**
 * modificar a informação de disponibilidade em estoque (is_in_stock) das variações de produtos, baseado em uma verificação customizada
 */
add_filter( "woocommerce_available_variation", function( $variation_data = array(), $product = "", $variation = "" ){
    if( is_array( $variation_data ) && isset( $variation_data["is_in_stock"] ) ){
        if( $variation_data["is_in_stock"] ){
            if( is_object( $variation ) ){
                $outofstock = check_stock_pedidos( $variation, true );
                if ( $outofstock ){
                    $variation_data["is_in_stock"] = false;
                }
            }
        }
    }
    return $variation_data;
}, 9999, 3 );

/*
function storefront_header_cart() {
    if ( storefront_is_woocommerce_activated() ) {
        if ( is_cart() || is_checkout() ) {
            $class = 'current-menu-item';
        } else {
            $class = '';
        }
    ?>
        <ul class="site-header-cart menu">
            <li class="<?php echo esc_attr( $class ); ?>">
                <?php storefront_cart_link(); ?>
            </li>
            <li>
                <?php the_widget( 'WC_Widget_Cart', 'title=' ); ?>
            </li>
        </ul>
    <?php
    }
}
*/


/* Desabilita um modal na página do carrinho - Storefront */
add_filter ('storefront_sticky_add_to_cart', '__return_false');

# LISTAGEM DE PRODUTOS - ORDENAÇÃO


// Remove itens do select nas páginas de listagem
function my_woocommerce_catalog_orderby( $orderby ) {
    unset($orderby["rating"]); // Remove "por avaliação"
    return $orderby;
}
add_filter( "woocommerce_catalog_orderby", "my_woocommerce_catalog_orderby", 20 );

// Adiciona a opção "Ordem alfabética" ao menu de ordenação
function antid_catalog_orderby($args){
    $new_args = array();
    $new_args['post_title'] = 'Ordem alfabética';
    foreach( $args as $index => $arg ){
        $new_args[$index] = $arg;
    }
    return $new_args;
}
add_filter( 'woocommerce_catalog_orderby', 'antid_catalog_orderby' );

// Define o comportamento da ordenação personalizada
function definir_ordenacao_padrao_woocommerce($args) {
    if ( is_shop() || is_product_category() ) {
        // Verifica se o usuário escolheu a ordenação personalizada
        if ( isset($_GET['orderby']) && $_GET['orderby'] === 'post_title' ) {
            $args['orderby'] = 'title';
            $args['order'] = 'ASC';
        } else {
            // Ordenação padrão
            $args['orderby'] = 'date';
            $args['order'] = 'DESC';
        }

        $args['meta_key'] = ''; // Limpa meta_key
    }

    return $args;
}
add_filter('woocommerce_get_catalog_ordering_args', 'definir_ordenacao_padrao_woocommerce');

