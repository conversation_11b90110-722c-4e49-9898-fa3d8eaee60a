<?php 

function antid_load_styles() {

   wp_register_style( 'slick-style', get_stylesheet_directory_uri() .'/stylesheets/slick.css');
   wp_enqueue_style( 'slick-style' );

   wp_register_style( 'slick-theme', get_stylesheet_directory_uri() .'/stylesheets/slick-theme.css');
  wp_enqueue_style( 'slick-theme' );
    
   wp_enqueue_script( 'slick-script', get_stylesheet_directory_uri() .'/js/slick.min.js', array( 'jquery' ) );

   wp_enqueue_script( 'slick-config', get_stylesheet_directory_uri() .'/js/slick-config.js', array( 'jquery' ) );

   wp_enqueue_script( 'main', get_stylesheet_directory_uri() .'/js/main.js');

}
add_action('wp_enqueue_scripts', 'antid_load_styles');




function load_fonts() {
    wp_register_style('googleFonts', 'https://fonts.googleapis.com/css?family=Titillium+Web:400,700,300&subset=latin,latin-ext&display=swap');
    wp_enqueue_style( 'googleFonts');
}
add_action('wp_footer', 'load_fonts');



/*
function antidesign_fix_nc_wishlist(){
    ?>
    <script src="<?php echo get_stylesheet_directory_uri(); ?>/js/jquery-2.1.4.min.js"></script>
    <?php
}
add_action( 'wp_footer', 'antidesign_fix_nc_wishlist', 9 );
*/



/**
 * Remov a barra de navegação no MOBILE do tema Store Front.
 */
function jk_remove_storefront_handheld_footer_bar() {
  remove_action( 'storefront_footer', 'storefront_handheld_footer_bar', 999 );
}
add_action( 'init', 'jk_remove_storefront_handheld_footer_bar' );