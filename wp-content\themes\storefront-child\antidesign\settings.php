<?php

/**
* ILC Tabbed Settings Page
*/
$arr_campos_cores = array( 'fundo' => 'Fundo', 'fundo_menu' => "Fundo Menu", 'link_menu' => "Link Menu", 'link_paginas' => "Link Páginas", 'textos' => "Textos" );
$arr_campos_cores_padrao = array( 'fundo' => '#ffffff', 'fundo_menu' => "#ffffff", 'link_menu' => "#000", 'link_paginas' => "#000", 'textos' => "#000" );

add_action( 'init', 'ilc_admin_init' );
add_action( 'admin_menu', 'ilc_settings_page_init' );

function ilc_admin_init() {
   $settings = get_option( "ilc_theme_settings" );
   if ( empty( $settings ) ) {
       $settings = array(
           'antid_gallery' => array()
       );
       add_option( "ilc_theme_settings", $settings, '', 'yes' );
       }

}

function ilc_settings_page_init() {
   $theme_data = get_theme_data( TEMPLATEPATH . '/style.css' );

   //$settings_page = add_theme_page( $theme_data['Name']. ' Theme Settings', $theme_data['Name']. ' Theme Settings', 'edit_theme_options', 'theme-settings', 'ilc_settings_page' );

      $settings_page = add_menu_page( 'Sliders', 'Sliders', 'edit_theme_options', 'theme-settings', 'ilc_settings_page' );

   add_action( "load-{$settings_page}", 'ilc_load_settings_page' );
}


function ilc_load_settings_page() {
       if ( isset( $_GET['excluir'] ) ){
           $excluir = $_GET['excluir'];
           wp_redirect( remove_query_arg( 'excluir' ) );
           exit;
       }
   else if ( $_POST["ilc-settings-submit"] == 'Y' ) {
       check_admin_referer( "ilc-settings-page" );
       ilc_save_theme_settings();
       $url_parameters = isset($_GET['tab'])? 'updated=true&tab='.$_GET['tab'] : 'updated=true';
               if ( isset( $_GET['gallery'] ) ){
                   $url_parameters .= "&gallery=".$_GET['gallery'];
               }
wp_redirect(admin_url('admin.php?page=theme-settings&'.$url_parameters));
       exit;
   }
}

function ilc_save_theme_settings() {
   global $pagenow,$arr_campos_cores,$arr_campos_cores_padrao;
   $settings = get_option( "ilc_theme_settings" );
       $settings_gallery = get_option( "ilc_gallery_settings" );
   if ( $settings_gallery == "" ){
           $settings_gallery = array();
       }
   if ( $pagenow == 'admin.php' && $_GET['page'] == 'theme-settings' ){
       if ( isset ( $_GET['tab'] ) )
           $tab = $_GET['tab'];
       else
           $tab = 'homepage-gallery';

       switch ( $tab ){
                       case 'homepage-gallery' :
                               if ( $settings['antid_gallery'] == "" ){
                                   $settings['antid_gallery'] = array();
                               }
                               if ( !isset( $_POST['antid_fsn_btn_hid'] ) ){
                                   $settings['antid_gallery'] = array();
                               } else {
                                   $arr_gallery = array();
                                   if ( !isset( $_POST['antid_fsn_btn_hid'] ) ){
                                       $settings['antid_gallery'] = array();
                                   } else {
                                       if ( is_array( $_POST['antid_fsn_btn_hid'] ) ){
                                           foreach( $_POST['antid_fsn_btn_hid'] as $image_id ){
$arr_gallery[$image_id] = array();
                                               $name_titulo = "antid_titulo_" . $image_id;
                                               $name_sub = "antid_sub_" . $image_id;
                                               $name_link = "antid_link_" . $image_id;
                                               if ( isset( $_POST[$name_titulo] ) && "" != $_POST[$name_titulo] ){
$arr_gallery[$image_id]['titulo'] = $_POST[$name_titulo];
                                               }
                                               if ( isset( $_POST[$name_sub] ) && "" != $_POST[$name_sub] ){
$arr_gallery[$image_id]['sub'] = $_POST[$name_sub];
                                               }
                                               if ( isset( $_POST[$name_link] ) && "" != $_POST[$name_link] ){
$arr_gallery[$image_id]['link'] = $_POST[$name_link];
                                               }
                                           }
                                       } else {
                                           $image_id = $_POST['antid_fsn_btn_hid'];
                                           $arr_gallery[$image_id] = array();
                                           $name_titulo = "antid_titulo_" . $image_id;
                                           $name_sub = "antid_sub_" . $image_id;
                                           $name_link = "antid_link_" . $image_id;
                                           if ( isset( $_POST[$name_titulo] ) && "" != $_POST[$name_titulo] ){
$arr_gallery[$image_id]['titulo'] = $_POST[$name_titulo];
                                           }
                                           if ( isset( $_POST[$name_sub] ) && "" != $_POST[$name_sub] ){
$arr_gallery[$image_id]['sub'] = $_POST[$name_sub];
                                           }
                                           if ( isset( $_POST[$name_link] ) && "" != $_POST[$name_link] ){
$arr_gallery[$image_id]['link'] = $_POST[$name_link];
                                           }
                                       }
                                   }
                                   $settings['antid_gallery'] = $arr_gallery;
                               }
           break;
                       case 'galleries' :
                               if ( isset( $_GET['gallery'] ) ){
                                   $_gallery = $_GET['gallery'];

                                   if ( !isset( $settings_gallery[$_gallery] ) ){
                                       $settings_gallery[$_gallery] = array();
$settings_gallery[$_gallery]['gallery'] = array();
                                   } else if ( !isset( $settings[$_gallery]['gallery'] ) ){
$settings_gallery[$_gallery]['gallery'] = array();
                                   }
$settings_gallery[$_gallery]['exibir'] = false;

                                   if ( isset( $_POST["enable_gallery"] ) && "1" == $_POST["enable_gallery"] ){
$settings_gallery[$_gallery]['exibir'] = true;
                                   }

                                   $arr_gallery = array();
                                   if ( !isset( $_POST['antid_fsn_btn_hid'] ) ){
$settings_gallery[$_gallery]['gallery'] = array();
                                   } else {
$settings_gallery[$_gallery]['gallery'] = array();

                                       if ( is_array( $_POST['antid_fsn_btn_hid'] ) ){

                                           foreach( $_POST['antid_fsn_btn_hid'] as $image_id ){
$arr_gallery[$image_id] = array();
                                               $name_titulo = "antid_titulo_" . $image_id;
                                               $name_sub = "antid_sub_" . $image_id;
                                               $name_link = "antid_link_" . $image_id;
                                               if ( isset( $_POST[$name_titulo] ) && "" != $_POST[$name_titulo] ){
$arr_gallery[$image_id]['titulo'] = $_POST[$name_titulo];
                                               }
                                               if ( isset( $_POST[$name_sub] ) && "" != $_POST[$name_sub] ){
$arr_gallery[$image_id]['sub'] = $_POST[$name_sub];
                                               }
                                               if ( isset( $_POST[$name_link] ) && "" != $_POST[$name_link] ){
$arr_gallery[$image_id]['link'] = $_POST[$name_link];
                                               }
                                           }
                                       } else {
                                           $image_id = $_POST['antid_fsn_btn_hid'];
                                           $arr_gallery[$image_id] = array();
                                           $name_titulo = "antid_titulo_" . $image_id;
                                           $name_sub = "antid_sub_" . $image_id;
                                           $name_link = "antid_link_" . $image_id;
                                           if ( isset( $_POST[$name_titulo] ) && "" != $_POST[$name_titulo] ){
$arr_gallery[$image_id]['titulo'] = $_POST[$name_titulo];
                                           }
                                           if ( isset( $_POST[$name_sub] ) && "" != $_POST[$name_sub] ){
$arr_gallery[$image_id]['sub'] = $_POST[$name_sub];
                                           }
                                           if ( isset( $_POST[$name_link] ) && "" != $_POST[$name_link] ){
$arr_gallery[$image_id]['link'] = $_POST[$name_link];
                                           }
                                       }
                                   }
$settings_gallery[$_gallery]['gallery'] = $arr_gallery;
                               }
           break;
       }
   }

   if( !current_user_can( 'unfiltered_html' ) ){
       if ( $settings['endereco']  )
           $settings['endereco'] = stripslashes( esc_textarea( wp_filter_post_kses( $settings['endereco'] ) ) );
       if ( $settings['endereco']  )
           $settings['endereco'] = stripslashes( esc_textarea( wp_filter_post_kses( $settings['facebook'] ) ) );
       if ( $settings['ilc_intro'] )
           $settings['ilc_intro'] = stripslashes( esc_textarea( wp_filter_post_kses( $settings['ilc_intro'] ) ) );
   }

   $updated = update_option( "ilc_theme_settings", $settings );
       $updated2 = update_option( "ilc_gallery_settings", $settings_gallery );
}

function ilc_admin_tabs( $current = 'homepage-gallery' ) {
   $tabs = array( 'homepage-gallery' => 'Galeria Homepage', 'galleries' => 'Categoria' );
   $links = array();
   echo '<div id="icon-themes" class="icon32"><br></div>';
   echo '<h2 class="nav-tab-wrapper">';
   foreach( $tabs as $tab => $name ){
       $class = ( $tab == $current ) ? ' nav-tab-active' : '';
       echo "<a class='nav-tab$class' href='?page=theme-settings&tab=$tab'>$name</a>";

   }
   echo '</h2>';
}

function ilc_settings_page() {
   global $pagenow,$arr_campos_cores,$arr_campos_cores_padrao;

   $settings = get_option( "ilc_theme_settings" );
   $theme_data = get_theme_data( TEMPLATEPATH . '/style.css' );
   ?>

   <div class="wrap">
       <h2><?php echo $theme_data['Name']; ?> Theme Settings</h2>

       <?php
           if ( 'true' == esc_attr( $_GET['updated'] ) ) echo '<div class="updated" ><p>Theme Settings updated.</p></div>';

           if ( isset ( $_GET['tab'] ) ) ilc_admin_tabs($_GET['tab']); else ilc_admin_tabs('homepage-gallery');
       ?>

       <div id="poststuff">
           <form method="post" action="<?php admin_url( 'admin.php?page=theme-settings' ); ?>">
               <?php
               wp_nonce_field( "ilc-settings-page" );
                               $antid_submit_action = "Atualizar";

               if ( $pagenow == 'admin.php' && $_GET['page'] == 'theme-settings' ){

                   if ( isset ( $_GET['tab'] ) ) $tab = $_GET['tab'];
                   else $tab = 'homepage-gallery';

                   echo '<table class="form-table">';
                   switch ( $tab ){
                                               case 'galleries' :
                                                   $antid_hide_btn = false;
                                                   if ( ! isset( $_GET['gallery'] ) ){
$antid_submit_action = "Adicionar";
$antid_hide_btn = true;
                                                   ?>

                                               <?php
                                                   } else {
                                                       $__gallery = $_GET['gallery'];
$settings_gallery_full = get_option( "ilc_gallery_settings" );

                                                       if ( isset( $settings_gallery_full[$__gallery] ) && is_array( $settings_gallery_full[$__gallery] ) ){
$settings_gallery = $settings_gallery_full[$__gallery];
                                                       }

$checked_habilitar = '';
                                                       if ( isset( $settings_gallery["exibir"] ) && $settings_gallery["exibir"] == "1" ){
$checked_habilitar = ' checked="checked"';
                                                       }
$settings_gallery = $settings_gallery["gallery"];
                                                       $url_voltar = remove_query_arg("gallery");
                                                       ?>
                                                       <tr>
                                                           <td colspan="2">
<a href="<?php echo $url_voltar;?>" class="voltar-url">Voltar</a>
</td>
                                                       </tr>
                                                       <tr>
                               <th><label for="logo">Slider</label></th>
                               <td>
<div id="wpss_upload_image_thumb" class="wpss-file">
<?php
if ( is_array( $settings_gallery ) ){
foreach( $settings_gallery as $antid_logo_id => $item_gallery ){
if ( gettype( $antid_logo_id ) == "integer" ){
$image = wp_get_attachment_image_src ( $antid_logo_id, 'full' );
$image_src = "";
if ( is_array( $image ) && isset( $image[0] ) ){
$image_src = $image[0];
}
if( $image_src != "" ){
$antid_titulo = "";
if ( is_array( $item_gallery ) && isset( $item_gallery["titulo"] ) ){
$antid_titulo = $item_gallery["titulo"];
}
$antid_sub = "";
if ( is_array( $item_gallery ) && isset( $item_gallery["sub"] ) ){
$antid_sub = $item_gallery["sub"];
}
$antid_link = "";
if ( is_array( $item_gallery ) && isset( $item_gallery["link"] ) ){
$antid_link = $item_gallery["link"];
}
?>
<div class="image_slider" data-id="<?php echo $antid_logo_id; ?>">
<img class="wpss_img" src="<?php echo $image_src; ?>" data-id="<?php echo $antid_logo_id; ?>" width="500"/>
<br/>
Título:<input type="text" name="antid_titulo_<?php echo $antid_logo_id;?>" value="<?php echo $antid_titulo;?>"/><br/>
Subtítulo:<input type="text" name="antid_sub_<?php echo $antid_logo_id;?>" value="<?php echo $antid_sub;?>"/><br/>
Link:<input type="text" name="antid_link_<?php echo $antid_logo_id;?>" value="<?php echo $antid_link;?>"/><br/>
<a href="#" data-id="<?php echo $antid_logo_id;?>" class="btn-excluir">Excluir</a>
</div>
<?php
}
}
}
}

$defaultImage = '<img src="'.get_stylesheet_directory_uri().'/antidesign/images/default.png">'; ?>
</div>
<div id="wpss-hidden_fields">
<?php
if ( is_array( $settings_gallery ) ){
foreach( $settings_gallery as $antid_logo_id => $item_gallery ){
if ( gettype( $antid_logo_id ) == "integer" ){

?>
<input id="antid_fsn_btn_hid" type="hidden" size="36" name="antid_fsn_btn_hid[]" value="<?php echo $antid_logo_id;?>" class="wpss_text wpss-file wpss_hide" />
<?php
}
}
}
?>
</div>
<input id="antid_fsn_btn" type="button" value="Upload Image" class="image_uploader gallery_nocat" />
                               </td>
                           </tr>
                           <?php
                                                   }
                                               break;
                                               case 'homepage-gallery' :
                           ?>
                                                       <tr>
                               <th><label for="logo">Galeria</label></th>
                               <td>
<div id="wpss_upload_image_thumb" class="wpss-file">
<?php
$settings_gallery = $settings['antid_gallery'];
if ( is_array( $settings_gallery ) ){
foreach( $settings_gallery as $antid_logo_id => $item_gallery ){
if ( gettype( $antid_logo_id ) == "integer" ){
$image = wp_get_attachment_image_src ( $antid_logo_id, 'full' );
$image_src = "";
if ( is_array( $image ) && isset( $image[0] ) ){
$image_src = $image[0];
}
if( $image_src != "" ){
$antid_titulo = "";
if ( is_array( $item_gallery ) && isset( $item_gallery["titulo"] ) ){
$antid_titulo = $item_gallery["titulo"];
}
$antid_sub = "";
if ( is_array( $item_gallery ) && isset( $item_gallery["sub"] ) ){
$antid_sub = $item_gallery["sub"];
}
$antid_link = "";
if ( is_array( $item_gallery ) && isset( $item_gallery["link"] ) ){
$antid_link = $item_gallery["link"];
}
?>
<div class="image_slider" data-id="<?php echo $antid_logo_id; ?>">
<img class="wpss_img" src="<?php echo $image_src; ?>" data-id="<?php echo $antid_logo_id; ?>" width="500"/>
<br/>
Título:<input type="text" name="antid_titulo_<?php echo $antid_logo_id;?>" value="<?php echo $antid_titulo;?>"/><br/>
Subtítulo:<input type="text" name="antid_sub_<?php echo $antid_logo_id;?>" value="<?php echo $antid_sub;?>"/><br/>
Link:<input type="text" name="antid_link_<?php echo $antid_logo_id;?>" value="<?php echo $antid_link;?>"/><br/>
<a href="#" data-id="<?php echo $antid_logo_id;?>" class="btn-excluir">Excluir</a>
</div>
<?php
}
}
}
}

$defaultImage = '<img src="'.get_stylesheet_directory_uri().'/antidesign/images/default.png">'; ?>
</div>
<div id="wpss-hidden_fields">
<?php
if ( is_array( $settings_gallery ) ){
foreach( $settings_gallery as $antid_logo_id => $item_gallery ){
if ( gettype( $antid_logo_id ) == "integer" ){ ?>
<input id="antid_fsn_btn_hid" type="hidden" size="36" name="antid_fsn_btn_hid[]" value="<?php echo $antid_logo_id;?>" class="wpss_text wpss-file wpss_hide" />
<?php
}
}
}
?>
</div>
<input id="antid_fsn_btn" type="button" value="Upload Image" class="image_uploader gallery_nocat" />
                               </td>
                           </tr>
                           <?php
                       break;
                   }
                   echo '</table>';
               }
               ?>
               <p class="submit" style="clear: both;">
                                       <?php if ( !$antid_hide_btn ){ ?>
                   <input type="submit" name="Submit" class="button-primary" value="<?php echo $antid_submit_action;?>" />
                                       <?php } ?>
                   <input type="hidden" name="ilc-settings-submit" value="Y" />
               </p>
           </form>
                   <?php
                       if ( $pagenow == 'admin.php' && $_GET['page'] == 'theme-settings' ){
                           if ( isset ( $_GET['tab'] ) ) $tab = $_GET['tab'];
                           else $tab = 'homepage-gallery';

                           echo '<table class="form-table">';
                           switch ( $tab ){
                               case 'galleries' :
                                   if ( ! isset( $_GET['gallery'] ) ){
                                   ?>
                       <table>
                           <tr>
                               <th>Categorias</th>
                           </tr>

                               <?php
                                   $antid_url = admin_url( "admin.php?page=".$_GET['page'].'&tab='.$_GET['tab'] );

                                   $antid_post_per_page = 10;

                                   if(isset($_GET['p'])){
                                       $antid_p = intval($_GET['p']);
                                       if ( ! $antid_p ) {
                                           $antid_p = 1;
                                       }
                                   }else{
                                       $antid_p = 1;
                                   }

                                   $args = array( 'parent' => 0 );
                                   $categories = get_terms( 'product_cat', $args );
                                   //print_r( $categories );

                                   $antid_found_post = count( $categories );

                                   $antid_offset      = $antid_post_per_page * ( $antid_p - 1) ;

                                   $args = array( 'offset' => $antid_offset, 'order'        => 'ASC', 'orderby' => 'menu_order', 'number' => $antid_post_per_page, 'parent' => 0 );

                                   $categories = get_terms( 'product_cat', $args );
                                   $antid_url_full = $antid_url . '&p='.$antid_p;
                                   foreach( $categories as $category ){
                                       if ( is_object( $category ) && isset( $category->term_id ) && isset( $category->name ) ){
                                           $term_id = $category->term_id;
                                           $cat_name = $category->name;
                                           $cat_link = get_term_link( $category );
                                           ?>
                               <tr>
                                   <td><?php echo $cat_name;?></td>
                                   <td><a href="<?php echo $antid_url_full;?>&gallery=<?php echo $term_id; ?>">Imagens</a></td>
                                   <td><a href="<?php echo $cat_link;?>" target="_blank">Visualizar</a></td>
                               </tr>
                           <?php
                                       }
                                   }


                               ?>
                               <tr>
                                   <td>
                                   <?php


                                   if ( $antid_found_post == 0 && $antid_p > 1 ){
                                       echo '<meta http-equiv="refresh" content="1;URL='.$antid_url.'">';
                                   }

                                   $antid_total_post = ceil( $antid_found_post / $antid_post_per_page );

                                   $antid_lpm1 = $antid_found_post - 1;

                                   $antid_prev = $antid_p - 1;
                                   $antid_next = $antid_p + 1;



                                   echo pagination( $antid_total_post, $antid_p, $antid_lpm1, $antid_prev, $antid_next, $antid_url );

                                   ?>
                                   </td>
                               </tr>

                       </table>


                                   <?php
                                   }
                               break;
                           }
                       }
           ?>
       </div>

   </div>
<?php
}



function pagination($totalposts,$p,$lpm1,$prev,$next,$url){
   $adjacents = 3;
   if($totalposts > 1)
   {
       $pagination .= "<center><div>";
       //previous button
       if ($p > 1)
       $pagination.= "<a href=\"".$url."&p=$prev\"><</a> ";
       else
       $pagination.= "<span class=\"disabled\"><</span> ";
       if ($totalposts < 7 + ($adjacents * 2)){
           for ($counter = 1; $counter <= $totalposts; $counter++){
               if ($counter == $p)
               $pagination.= "<span class=\"current\">$counter</span>";
               else
               $pagination.= " <a href=\"".$url."&p=$counter\">$counter</a> ";}
       }elseif($totalposts > 5 + ($adjacents * 2)){
           if($p < 1 + ($adjacents * 2)){
               for ($counter = 1; $counter < 4 + ($adjacents * 2); $counter++){
                   if ($counter == $p)
                   $pagination.= " <span class=\"current\">$counter</span> ";
                   else
                   $pagination.= " <a href=\"".$url."&p=$counter\">$counter</a> ";
               }
               $pagination.= " ... ";
               $pagination.= " <a href=\"".$url."&p=$lpm1\">$lpm1</a> ";
               $pagination.= " <a href=\"".$url."&p=$totalposts\">$totalposts</a> ";
           }
           //in middle; hide some front and some back
           elseif($totalposts - ($adjacents * 2) > $p && $p > ($adjacents * 2)){
               $pagination.= " <a href=\"".$url."&p=1\">1</a> ";
               $pagination.= " <a href=\"".$url."&p=2\">2</a> ";
               $pagination.= " ... ";
               for ($counter = $p - $adjacents; $counter <= $p + $adjacents; $counter++){
                   if ($counter == $p)
                   $pagination.= " <span class=\"current\">$counter</span> ";
                   else
                   $pagination.= " <a href=\"".$url."&p=$counter\">$counter</a> ";
               }
               $pagination.= " ... ";
               $pagination.= " <a href=\"".$url."&p=$lpm1\">$lpm1</a> ";
               $pagination.= " <a href=\"".$url."&p=$totalposts\">$totalposts</a> ";
           }else{
               $pagination.= " <a href=\"".$url."&p=1\">1</a> ";
               $pagination.= " <a href=\"".$url."&p=2\">2</a> ";
               $pagination.= " ... ";
               for ($counter = $totalposts - (2 + ($adjacents * 2)); $counter <= $totalposts; $counter++){
                   if ($counter == $p)
                   $pagination.= " <span class=\"current\">$counter</span> ";
                   else
                   $pagination.= " <a href=\"".$url."&p=$counter\">$counter</a> ";
               }
           }
       }
       if ($p < $counter - 1)
       $pagination.= " <a href=\"".$url."&p=$next\">></a>";
       else
       $pagination.= " <span class=\"disabled\">></span>";
       $pagination.= "</center>\n";
   }
   return $pagination;
}

?>