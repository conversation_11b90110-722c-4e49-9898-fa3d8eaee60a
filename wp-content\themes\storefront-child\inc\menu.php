<?php 

if (! function_exists ( "antid_add_slug_class_to_menu_item" ) ){

    function antid_add_slug_class_to_menu_item($classes,$item,$args){

        $cat_type = 'product_cat';
        
        $container = 'primary-navigation';
        
        $current_category = get_queried_object();
        
        if (is_archive($cat_type) ){

            if ( is_object($item) && isset($item->object_id) )
                    $object_id = $item->object_id;
                
            
            if ( $args->container_class == $container && isset( $object_id ) ){

                
                if ( is_object( $current_category ) && isset( $current_category->parent ) ){

                    
                    if ( $current_category->parent != 0 ){

                        $_parent = $current_category->parent;
                        
                        while ( $_parent != 0 ){

                            $current_category = get_term( $_parent, $cat_type );
                            
                            
                            
                            $_parent = $current_category->parent;
                            
                        }

                        if ( is_object( $current_category ) && isset( $current_category->term_id ) ){

                            
                            $current_ID = $current_category->term_id;
                            
                            if ( $object_id == $current_ID ){

                                $classes[] = 'current-menu-item';
                                
                            }

                        }

                        
                        
                    }

                }

            }

            
        }
         else if ( is_product() && isset( $current_category ) && is_object($current_category) && isset( $current_category->ID ) ){

            $post_id = $current_category->ID;
            
            
            $cats = wp_get_object_terms($post_id, 'product_cat');
            
            //print_r($cats);
            
            //print_r($item);
            
            if ( is_object($item) && isset($item->object_id) )
                    $object_id = $item->object_id;
                
            
            //echo $object_id.'-aaaa<br>';
                
            $current = false;
            
            foreach($cats as $cat){

             
                if (is_object($cat) && isset( $cat->term_id ) && isset( $cat->slug ) ){

//                    echo $cat->term_id.'-'.$object_id.'-'.$cat->slug.'<br>';
                	
                    if ( $cat->term_id == $object_id ){

                        $current = true;
                        
                    }
                     else if ( isset( $cat->parent ) && $cat->parent != 0 ) {

                        $__parent = $cat->parent;
                        
                        while ( $__parent != 0 ){

                            $__term = get_term( $__parent, "product_cat" );
                            
                            
                            if ( isset( $__term->term_id ) && is_object($__term) && $object_id == $__term->term_id ){

                                $current = true;
                                
                            }

                            if ( isset( $__term->parent ) && $__term->parent != 0 ){

                                $__parent = $__term->parent;
                                
                            }
                             else {

                                $__parent = 0;
                                
                            }

                        }

                    }

                    
                    
                }

            }

            if ( $current ){

                $classes[] = 'current-menu-item';
                
            }

        }

        return $classes;
        
    }

    add_filter( 'nav_menu_css_class', 'antid_add_slug_class_to_menu_item', 30, 3 );
    
}

add_action('antid_display_menu','antid_display_menu');



add_action("menu_institucional", "storefront_secondary_navigation");

function storefront_secondary_navigation() {

?>
<div class="fixedmenu">
    <nav class="secondary-navigation" role="navigation">
        <?php wp_nav_menu( array( 'theme_location' => 'secondary', 'fallback_cb' => '' ) );
         ?>
    </nav>
</div><!-- #site-navigation -->
<?php
}



function child_theme_init() {

    remove_action( 'storefront_header', 'storefront_primary_navigation', 30 );
    
    add_action( 'storefront_header', 'storefront_primary_navigation', 50 );
    
}

add_action( 'init', 'child_theme_init' );



// Botão de logout no TOPMENUBAR
function add_loginout_link( $items, $args ) {

    global $woocommerce;
    
    if( is_object( $woocommerce ) && isset( $woocommerce->version ) && !empty( $woocommerce->version ) ){

        $version = $woocommerce->version;
        
        if( is_user_logged_in() && $args->theme_location == 'secondary' ){

            if ( version_compare( $version, "3.0", '>=' ) ){

                $items .= '<li class="logout"><a href="'. wp_logout_url( get_permalink( wc_get_page_id( 'myaccount' ) ) ) .'">Sair</a></li>';
                
            }
             else {

                $items .= '<li class="logout"><a href="'. wp_logout_url( get_permalink( woocommerce_get_page_id( 'myaccount' ) ) ) .'">Sair</a></li>';
                
            }

        }

    }

    return $items;
    
}

add_filter( 'wp_nav_menu_items', 'add_loginout_link', 10, 2 );



function antid_categorias_secundarias(){

   $current_obj = get_queried_object();
   
   $html_links = "";
   
   if ( is_object( $current_obj ) && isset( $current_obj->taxonomy ) && "product_cat" == $current_obj->taxonomy ){

       //print_r( $current_obj );
   	
       if ( isset( $current_obj->term_id ) ){

           $term_id_parent = $current_obj->term_id;
           
           $categories = get_terms( 'product_cat', array(
               'parent' => $term_id_parent
           ) );
           
           if ( empty( $categories ) ){

               if ( isset( $current_obj->term_id ) ){

                   $term_id_parent = $current_obj->parent;
                   
                   $categories = get_terms( 'product_cat', array(
                       'parent' => $term_id_parent
                   ) );
                   
               }

           }

           if ( is_array( $categories ) ){

               foreach( $categories as $cat ){

                   //print_r( $cat );
               	
                   $term_link = get_term_link( $cat );
                   
                   if ( $term_link == "" ){

                       $term_link = "#";
                       
                   }

                   if ( is_object( $cat ) && isset( $cat->name ) ){

                       $cat_name = $cat->name;
                       
                       $html_links .= '<a href="' . $term_link . '" class="botao easy">'.$cat_name.'</a>';
                       
                   }


               }

           }

       }

   }

   if( $html_links != "" ){

       $html_links = '<aside id="submenu" class="widget submenu"><h3 class="widget-title">Categorias</h3>'. $html_links . '</aside>';
       
       echo $html_links;
       
   }

}

add_action( 'antid_sub_category', 'antid_categorias_secundarias' );
