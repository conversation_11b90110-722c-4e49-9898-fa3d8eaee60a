# Copyright (C) 2024 <PERSON>
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: <PERSON> for WooCommerce 4.2.5\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/woocommerce-correios\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-02-17T15:03:12-03:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: woocommerce-correios\n"

#. Plugin Name of the plugin
#: includes/admin/views/html-admin-missing-dependencies.php:22
msgid "<PERSON> - <PERSON> for WooCommerce"
msgstr ""

#. Plugin URI of the plugin
msgid "https://github.com/claudiosanches/woocommerce-correios"
msgstr ""

#. Description of the plugin
msgid "Adds Correios shipping methods to your WooCommerce store."
msgstr ""

#. Author of the plugin
msgid "Claudio Sanches"
msgstr ""

#. Author URI of the plugin
msgid "https://claudiosanches.com"
msgstr ""

#. translators: %s: method title
#: includes/abstracts/class-wc-correios-shipping-carta.php:42
#: includes/abstracts/class-wc-correios-shipping-impresso.php:87
#: includes/abstracts/class-wc-correios-shipping.php:162
msgid "%s is a shipping method from Correios."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:80
msgid "-- Select a shipping class --"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:96
#: includes/abstracts/class-wc-correios-shipping-impresso.php:124
#: includes/abstracts/class-wc-correios-shipping-international.php:65
#: includes/abstracts/class-wc-correios-shipping.php:240
#: includes/emails/class-wc-correios-tracking-email.php:96
#: includes/integrations/class-wc-correios-integration.php:150
#: includes/integrations/class-wc-correios-integration.php:168
#: includes/shipping/class-wc-correios-shipping-cws.php:194
msgid "Enable/Disable"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:98
#: includes/abstracts/class-wc-correios-shipping-impresso.php:126
#: includes/abstracts/class-wc-correios-shipping-international.php:67
#: includes/abstracts/class-wc-correios-shipping.php:242
#: includes/shipping/class-wc-correios-shipping-cws.php:196
msgid "Enable this shipping method"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:102
#: includes/abstracts/class-wc-correios-shipping-impresso.php:130
#: includes/abstracts/class-wc-correios-shipping-international.php:71
#: includes/abstracts/class-wc-correios-shipping.php:246
#: includes/shipping/class-wc-correios-shipping-cws.php:200
msgid "Title"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:104
#: includes/abstracts/class-wc-correios-shipping-impresso.php:132
#: includes/abstracts/class-wc-correios-shipping-international.php:73
#: includes/abstracts/class-wc-correios-shipping.php:248
#: includes/shipping/class-wc-correios-shipping-cws.php:202
msgid "This controls the title which the user sees during checkout."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:109
#: includes/abstracts/class-wc-correios-shipping-impresso.php:137
#: includes/abstracts/class-wc-correios-shipping-international.php:78
#: includes/abstracts/class-wc-correios-shipping.php:253
#: includes/shipping/class-wc-correios-shipping-cws.php:218
msgid "Behavior Options"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:114
#: includes/abstracts/class-wc-correios-shipping-impresso.php:142
#: includes/abstracts/class-wc-correios-shipping-international.php:91
#: includes/abstracts/class-wc-correios-shipping.php:266
#: includes/shipping/class-wc-correios-shipping-cws.php:231
msgid "Shipping Class"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:116
#: includes/abstracts/class-wc-correios-shipping-impresso.php:144
msgid "Select for which shipping class this method will be applied."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:123
#: includes/abstracts/class-wc-correios-shipping-impresso.php:164
#: includes/abstracts/class-wc-correios-shipping-international.php:100
#: includes/abstracts/class-wc-correios-shipping.php:275
#: includes/shipping/class-wc-correios-shipping-cws.php:240
msgid "Delivery Time"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:125
#: includes/abstracts/class-wc-correios-shipping-impresso.php:166
#: includes/abstracts/class-wc-correios-shipping-international.php:102
#: includes/abstracts/class-wc-correios-shipping.php:277
#: includes/shipping/class-wc-correios-shipping-cws.php:242
msgid "Show estimated delivery time"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:126
#: includes/abstracts/class-wc-correios-shipping-impresso.php:167
#: includes/abstracts/class-wc-correios-shipping-international.php:103
#: includes/abstracts/class-wc-correios-shipping.php:278
#: includes/shipping/class-wc-correios-shipping-cws.php:243
msgid "Display the estimated delivery time in working days."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:131
#: includes/abstracts/class-wc-correios-shipping-impresso.php:172
msgid "Delivery Days"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:133
#: includes/abstracts/class-wc-correios-shipping-impresso.php:174
msgid "Working days to the estimated delivery."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:139
#: includes/abstracts/class-wc-correios-shipping-impresso.php:180
#: includes/abstracts/class-wc-correios-shipping-international.php:171
msgid "Extra Weight (g)"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:141
#: includes/abstracts/class-wc-correios-shipping-impresso.php:182
#: includes/abstracts/class-wc-correios-shipping-international.php:173
msgid "Extra weight in grams to add to the package total when quoting shipping costs."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:146
#: includes/abstracts/class-wc-correios-shipping-impresso.php:187
#: includes/abstracts/class-wc-correios-shipping-international.php:108
#: includes/abstracts/class-wc-correios-shipping.php:291
#: includes/shipping/class-wc-correios-shipping-cws.php:256
msgid "Handling Fee"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:148
#: includes/abstracts/class-wc-correios-shipping-impresso.php:189
#: includes/abstracts/class-wc-correios-shipping-international.php:110
#: includes/abstracts/class-wc-correios-shipping.php:293
#: includes/shipping/class-wc-correios-shipping-cws.php:258
msgid "Enter an amount, e.g. 2.50, or a percentage, e.g. 5%. Leave blank to disable."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:154
#: includes/abstracts/class-wc-correios-shipping-impresso.php:195
#: includes/abstracts/class-wc-correios-shipping-international.php:116
#: includes/abstracts/class-wc-correios-shipping.php:299
#: includes/shipping/class-wc-correios-shipping-cws.php:264
msgid "Optional Services"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:156
#: includes/abstracts/class-wc-correios-shipping-impresso.php:197
#: includes/abstracts/class-wc-correios-shipping-international.php:118
#: includes/abstracts/class-wc-correios-shipping.php:301
#: includes/shipping/class-wc-correios-shipping-cws.php:266
msgid "Use these options to add the value of each service provided by the Correios."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:160
#: includes/abstracts/class-wc-correios-shipping-impresso.php:201
#: includes/abstracts/class-wc-correios-shipping.php:305
#: includes/shipping/class-wc-correios-shipping-cws.php:270
msgid "Receipt Notice"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:162
#: includes/abstracts/class-wc-correios-shipping-impresso.php:203
#: includes/abstracts/class-wc-correios-shipping.php:307
#: includes/shipping/class-wc-correios-shipping-cws.php:272
msgid "Enable receipt notice"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:163
#: includes/abstracts/class-wc-correios-shipping-impresso.php:204
#: includes/abstracts/class-wc-correios-shipping.php:308
#: includes/shipping/class-wc-correios-shipping-cws.php:273
msgid "This controls whether to add costs of the receipt notice service."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:168
#: includes/abstracts/class-wc-correios-shipping-impresso.php:209
#: includes/abstracts/class-wc-correios-shipping.php:313
#: includes/shipping/class-wc-correios-shipping-cws.php:278
msgid "Own Hands"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:170
#: includes/abstracts/class-wc-correios-shipping-impresso.php:211
#: includes/abstracts/class-wc-correios-shipping.php:315
#: includes/shipping/class-wc-correios-shipping-cws.php:280
msgid "Enable own hands"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:171
#: includes/abstracts/class-wc-correios-shipping-impresso.php:212
#: includes/abstracts/class-wc-correios-shipping.php:316
#: includes/shipping/class-wc-correios-shipping-cws.php:281
msgid "This controls whether to add costs of the own hands service."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:176
#: includes/abstracts/class-wc-correios-shipping-impresso.php:217
#: includes/abstracts/class-wc-correios-shipping-international.php:178
#: includes/abstracts/class-wc-correios-shipping.php:402
#: includes/shipping/class-wc-correios-shipping-cws.php:330
msgid "Testing"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:181
#: includes/abstracts/class-wc-correios-shipping-impresso.php:222
#: includes/abstracts/class-wc-correios-shipping-international.php:183
#: includes/abstracts/class-wc-correios-shipping.php:407
#: includes/integrations/class-wc-correios-integration.php:137
#: includes/shipping/class-wc-correios-shipping-cws.php:335
msgid "Debug Log"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-carta.php:183
#: includes/abstracts/class-wc-correios-shipping-impresso.php:224
#: includes/abstracts/class-wc-correios-shipping-international.php:185
#: includes/abstracts/class-wc-correios-shipping.php:409
#: includes/shipping/class-wc-correios-shipping-cws.php:337
msgid "Enable logging"
msgstr ""

#. translators: %s: method title
#: includes/abstracts/class-wc-correios-shipping-carta.php:186
#: includes/abstracts/class-wc-correios-shipping-impresso.php:227
#: includes/abstracts/class-wc-correios-shipping-international.php:188
#: includes/abstracts/class-wc-correios-shipping.php:412
#: includes/shipping/class-wc-correios-shipping-cws.php:340
msgid "Log %s events, such as WebServices requests."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-impresso.php:151
msgid "Registry Type"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-impresso.php:153
msgid "Select for which registry type this method will be applied."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-impresso.php:157
msgid "-- Select a registry type --"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-impresso.php:158
msgid "Registro Nacional"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-impresso.php:159
msgid "Registro Módico"
msgstr ""

#. translators: %s: method title
#: includes/abstracts/class-wc-correios-shipping-international.php:30
msgid "%s is a international shipping method from Correios."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:83
#: includes/abstracts/class-wc-correios-shipping.php:258
#: includes/shipping/class-wc-correios-shipping-cws.php:223
msgid "Origin Postcode"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:85
#: includes/abstracts/class-wc-correios-shipping.php:260
#: includes/shipping/class-wc-correios-shipping-cws.php:225
msgid "The postcode of the location your packages are delivered from."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:93
#: includes/abstracts/class-wc-correios-shipping.php:268
#: includes/shipping/class-wc-correios-shipping-cws.php:233
msgid "If necessary, select a shipping class to apply this method."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:122
#: includes/abstracts/class-wc-correios-shipping.php:321
#: includes/shipping/class-wc-correios-shipping-cws.php:286
msgid "Declare Value for Insurance"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:124
#: includes/abstracts/class-wc-correios-shipping.php:323
#: includes/shipping/class-wc-correios-shipping-cws.php:288
msgid "Enable declared value"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:125
#: includes/abstracts/class-wc-correios-shipping.php:324
msgid "This controls if the price of the package must be declared for insurance purposes."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:130
msgid "User"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:132
msgid "Your Correios login. It's usually your idCorreios."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:137
msgid "Password"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:139
#: includes/abstracts/class-wc-correios-shipping.php:363
msgid "Your Correios password."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:144
#: includes/abstracts/class-wc-correios-shipping.php:368
#: includes/shipping/class-wc-correios-shipping-cws.php:296
msgid "Package Standard"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:146
#: includes/abstracts/class-wc-correios-shipping.php:370
#: includes/shipping/class-wc-correios-shipping-cws.php:298
msgid "Minimum measure for your shipping packages."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:150
#: includes/abstracts/class-wc-correios-shipping.php:374
#: includes/shipping/class-wc-correios-shipping-cws.php:302
msgid "Minimum Height (cm)"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:152
#: includes/abstracts/class-wc-correios-shipping.php:376
#: includes/shipping/class-wc-correios-shipping-cws.php:304
msgid "Minimum height of your shipping packages. Correios needs at least 2cm."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:157
#: includes/abstracts/class-wc-correios-shipping.php:381
#: includes/shipping/class-wc-correios-shipping-cws.php:309
msgid "Minimum Width (cm)"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:159
#: includes/abstracts/class-wc-correios-shipping.php:383
#: includes/shipping/class-wc-correios-shipping-cws.php:311
msgid "Minimum width of your shipping packages. Correios needs at least 11cm."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:164
#: includes/abstracts/class-wc-correios-shipping.php:388
#: includes/shipping/class-wc-correios-shipping-cws.php:316
msgid "Minimum Length (cm)"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping-international.php:166
#: includes/abstracts/class-wc-correios-shipping.php:390
#: includes/shipping/class-wc-correios-shipping-cws.php:318
msgid "Minimum length of your shipping packages. Correios needs at least 16cm."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:202
#: includes/integrations/class-wc-correios-integration.php:87
msgid "View logs."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:223
msgid "Any Shipping Class"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:224
msgid "No Shipping Class"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:283
#: includes/shipping/class-wc-correios-shipping-cws.php:248
msgid "Additional Days"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:285
#: includes/shipping/class-wc-correios-shipping-cws.php:250
msgid "Additional working days to the estimated delivery."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:329
msgid "Service Options"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:334
msgid "Service Code"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:336
msgid "Service code, use this for custom codes."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:342
msgid "Service Type"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:344
msgid "Choose between conventional or corporate service."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:349
msgid "Conventional"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:350
msgid "Corporate"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:354
msgid "Administrative Code"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:356
msgid "Your Correios login. It's usually your CNPJ."
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:361
msgid "Administrative Password"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:395
#: includes/shipping/class-wc-correios-shipping-cws.php:323
msgid "Extra Weight (kg)"
msgstr ""

#: includes/abstracts/class-wc-correios-shipping.php:397
#: includes/shipping/class-wc-correios-shipping-cws.php:325
msgid "Extra weight in kilograms to add to the package total when quoting shipping costs."
msgstr ""

#: includes/admin/class-wc-correios-admin-orders.php:51
#: includes/admin/views/html-meta-box-tracking-code.php:19
#: includes/admin/views/html-meta-box-tracking-code.php:41
msgid "Tracking code"
msgstr ""

#: includes/admin/class-wc-correios-admin-orders.php:99
msgid "Are you sure you want to remove this tracking code?"
msgstr ""

#: includes/admin/views/html-admin-missing-dependencies.php:22
msgid "depends on the last version of WooCommerce to work!"
msgstr ""

#: includes/admin/views/html-admin-missing-dependencies.php:25
msgid "Active WooCommerce"
msgstr ""

#: includes/admin/views/html-admin-missing-dependencies.php:34
msgid "Install WooCommerce"
msgstr ""

#. translators: %s: method title
#: includes/admin/views/html-admin-shipping-method-settings.php:20
msgid "More about %s."
msgstr ""

#. translators: %s: settings link labed "here"
#: includes/admin/views/html-admin-shipping-method-settings.php:33
msgid "This shipping method requires integration with the new Correios API, complete this integration %s. If you are seeing this message even after completing the integration, click on the \"Update Services List\" button to generate the list of services and be able to use this delivery method."
msgstr ""

#: includes/admin/views/html-admin-shipping-method-settings.php:34
msgid "here"
msgstr ""

#: includes/admin/views/html-admin-support.php:20
msgid "Support this plugin's author"
msgstr ""

#: includes/admin/views/html-admin-support.php:21
msgid "Apoia.se"
msgstr ""

#: includes/admin/views/html-admin-support.php:22
msgid "Be a member of Apoia.se and help in the development of many free plugins for WooCommerce, including the Brazilian Market on WooCommerce, depending on the amount of your support you can report bugs and vote on what will be prioritized in monthly releases and updates, in addition to having access to an exclusive support system for supporters that I intend to answer during working days between 12:00 and 19:00."
msgstr ""

#: includes/admin/views/html-admin-support.php:23
msgid "Become a member at Apoia.se"
msgstr ""

#: includes/admin/views/html-admin-support.php:24
#: includes/admin/views/html-admin-support.php:26
msgid "Make a review"
msgstr ""

#: includes/admin/views/html-admin-support.php:25
msgid "Help this plugin by rating with &#9733;&#9733;&#9733;&#9733;&#9733; on WordPress.org."
msgstr ""

#: includes/admin/views/html-list-table-tracking-code.php:15
#: includes/admin/views/html-meta-box-tracking-code.php:16
#: includes/admin/views/html-meta-box-tracking-code.php:37
#: templates/myaccount/tracking-codes.php:15
msgid "Tracking code:"
msgid_plural "Tracking codes:"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/views/html-meta-box-tracking-code.php:19
#: includes/admin/views/html-meta-box-tracking-code.php:41
msgid "Remove tracking code"
msgstr ""

#: includes/admin/views/html-meta-box-tracking-code.php:26
msgid "Add tracking code"
msgstr ""

#: includes/admin/views/html-meta-box-tracking-code.php:28
msgid "Add new tracking code"
msgstr ""

#: includes/admin/views/html-meta-box-tracking-code.php:35
msgid "Tracking codes:"
msgstr ""

#: includes/class-wc-correios-autofill-addresses.php:276
msgid "Missing postcode paramater."
msgstr ""

#: includes/class-wc-correios-autofill-addresses.php:283
#: includes/class-wc-correios-autofill-addresses.php:290
msgid "Invalid postcode."
msgstr ""

#. translators: 1: shipping method name 2: days to delivery
#: includes/class-wc-correios-orders.php:40
msgid "%1$s (delivery within %2$d working day after sending)"
msgid_plural "%1$s (delivery within %2$d working days after sending)"
msgstr[0] ""
msgstr[1] ""

#: includes/class-wc-correios-orders.php:57
msgid "Delivery forecast"
msgstr ""

#: includes/class-wc-correios-rest-api.php:75
msgid "Correios tracking code."
msgstr ""

#: includes/class-wc-correios.php:201
msgid "Settings"
msgstr ""

#: includes/class-wc-correios.php:202
msgid "Premium Support"
msgstr ""

#: includes/class-wc-correios.php:203
msgid "Contribute"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:38
msgid "Correios Tracking Code"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:40
msgid "This email is sent when configured a tracking code within an order."
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:62
msgid "Your order #{order_number} has been sent by Correios"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:71
msgid "Your order has been sent"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:80
msgid "Hi there. Your recent order on {site_title} has been sent by Correios."
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:82
msgid "To track your delivery, use the following the tracking code(s): {tracking_code}"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:84
msgid "The delivery service is the responsibility of the Correios, but if you have any questions, please contact us."
msgstr ""

#. translators: %s: list of placeholders
#: includes/emails/class-wc-correios-tracking-email.php:92
msgid "Available placeholders: %s"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:98
msgid "Enable this email notification"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:102
msgid "Subject"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:110
msgid "Email heading"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:118
msgid "Email content"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:126
msgid "Email type"
msgstr ""

#: includes/emails/class-wc-correios-tracking-email.php:128
msgid "Choose which format of email to send."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:52
msgid "Correios"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:96
msgid "Correios Web Services"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:98
msgid "Integrates with the new Correios API. Note that \"Username\", \"Access Code\" and \"Posting Card\" are required to make this integration to work properly."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:101
msgid "Environment"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:103
msgid "Select an environment for your integration with Correios API."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:106
msgid "Production"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:107
msgid "Staging"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:111
msgid "Portal Meu Correio's Username"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:113
msgid "Your Portal Meu Correio's username."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:117
msgid "Access Code"
msgstr ""

#. translators: %s: Correios URL
#: includes/integrations/class-wc-correios-integration.php:120
msgid "Your Correios API Access Code. You can generate an access code in %1$s for production or %2$s for staging."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:124
msgid "Posting Card"
msgstr ""

#. translators: 1: Correios URL
#: includes/integrations/class-wc-correios-integration.php:127
msgid "Your Correios Posting Card number. The number is 10 digits long and starts with two zeros. To check your Posting Card number, go to: %1$s"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:131
msgid "Generate/Update Services List"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:133
msgid "Update Services List"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:134
msgid "Generates a list with all services available for your Correios's contract."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:139
msgid "Enable logging for Correios API"
msgstr ""

#. translators: %s: log link
#: includes/integrations/class-wc-correios-integration.php:142
msgid "Log %s events, such as Web Services requests."
msgstr ""

#. translators: %s: log link
#: includes/integrations/class-wc-correios-integration.php:142
msgid "Correios API"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:145
msgid "Tracking History Table"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:147
msgid "Displays a table with informations about the shipping in My Account > View Order page. Required username and password that can be obtained with the Correios' commercial area."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:152
msgid "Enable Tracking History Table"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:156
msgid "Link Correios"
msgstr ""

#. translators: %s: Correios URL
#: includes/integrations/class-wc-correios-integration.php:159
msgid "Custom link to display tracking history of objects from Correios. By default uses %s"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:163
msgid "Autofill Addresses"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:165
msgid "Enable address autofill based on zipcode during checkout."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:170
msgid "Enable Autofill Addresses"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:174
msgid "Postcodes Validity"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:178
msgid "Defines how long a postcode will stay saved in the database before a new query."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:180
msgid "1 month"
msgstr ""

#. translators: %s number of months
#: includes/integrations/class-wc-correios-integration.php:182
#: includes/integrations/class-wc-correios-integration.php:184
#: includes/integrations/class-wc-correios-integration.php:186
#: includes/integrations/class-wc-correios-integration.php:188
#: includes/integrations/class-wc-correios-integration.php:190
#: includes/integrations/class-wc-correios-integration.php:192
#: includes/integrations/class-wc-correios-integration.php:194
#: includes/integrations/class-wc-correios-integration.php:196
#: includes/integrations/class-wc-correios-integration.php:198
#: includes/integrations/class-wc-correios-integration.php:200
#: includes/integrations/class-wc-correios-integration.php:202
msgid "%d months"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:203
msgid "Forever"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:207
msgid "Force Autofill"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:209
msgid "Enable Force Autofill"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:210
msgid "When enabled will autofill all addresses after the user finish to fill the postcode, even if the addresses are already filled."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:214
#: includes/integrations/class-wc-correios-integration.php:216
msgid "Empty Database"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:217
msgid "Delete all the saved postcodes in the database, use this option if you have issues with outdated postcodes."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:246
msgid "Are you sure you want to delete all postcodes from the database?"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:393
#: includes/integrations/class-wc-correios-integration.php:417
msgid "Missing parameters!"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:397
#: includes/integrations/class-wc-correios-integration.php:422
msgid "Invalid nonce!"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:404
msgid "Unable to retrieve services list! Review the Portal Meu Correio's Username, Access Code and Posting Card information, save the settings and try again."
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:407
msgid "Services list generated successfully!"
msgstr ""

#: includes/integrations/class-wc-correios-integration.php:431
msgid "Postcode database emptied successfully!"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-carta-registrada.php:26
msgid "Carta Registrada"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws-international.php:39
msgid "Correios International (New API)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws-international.php:40
msgid "Correios International shipping method."
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws-international.php:63
#: includes/shipping/class-wc-correios-shipping-cws.php:102
msgid "Not declare"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws-international.php:64
msgid "(030) Valor Declarado Internacional"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:46
msgid "Correios (New API)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:47
msgid "Correios shipping method."
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:70
#: includes/shipping/class-wc-correios-shipping-cws.php:214
msgid "Select a service..."
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:103
msgid "(019) Valor Declarado Nacional Premium e Expresso (use for SEDEX)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:104
msgid "(064) Valor Declarado Nacional Standard (use for PAC)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:105
msgid "(065) Valor Declarado Correios Mini Envios (use for SEDEX Mini)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:106
msgid "(075) Valor Declarado Expresso RFID (SEDEX)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:107
msgid "(076) Valor Declarado Standard RFID (PAC)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:207
msgid "Service"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:209
msgid "Select a service. It list all services available for your contract with Correios."
msgstr ""

#: includes/shipping/class-wc-correios-shipping-cws.php:289
msgid "This controls how the price of the package must be declared for insurance purposes."
msgstr ""

#: includes/shipping/class-wc-correios-shipping-documento-economico.php:34
msgid "Documento Econômico (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-documento-internacional-expresso.php:34
msgid "Documento Internacional Expresso (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-documento-internacional-premium.php:34
msgid "Documento Internacional Premium (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-documento-internacional-standard.php:34
msgid "Documento Internacional Standard (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-exporta-facil-economico.php:34
msgid "Exporta Fácil Econômico (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-exporta-facil-expresso.php:34
msgid "Exporta Fácil Expresso (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-exporta-facil-premium.php:34
msgid "Exporta Fácil Premium (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-exporta-facil-standard.php:34
msgid "Exporta Fácil Standard (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-impresso-normal.php:46
msgid "Impresso Normal"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-impresso-urgente.php:36
msgid "Impresso Urgente"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-pac.php:42
msgid "PAC (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-sedex-10-envelope.php:34
msgid "SEDEX 10 Envelope (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-sedex-10-pacote.php:34
msgid "SEDEX 10 Pacote (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-sedex-12.php:34
msgid "SEDEX 12 (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-sedex-hoje.php:34
msgid "SEDEX Hoje (Legacy)"
msgstr ""

#: includes/shipping/class-wc-correios-shipping-sedex.php:42
msgid "SEDEX (Legacy)"
msgstr ""

#. translators: %d: days to delivery
#: includes/wc-correios-functions.php:74
msgid "Delivery within %d working day"
msgid_plural "Delivery within %d working days"
msgstr[0] ""
msgstr[1] ""

#: includes/wc-correios-functions.php:108
msgid "System temporarily down. Please try again later."
msgstr ""

#: includes/wc-correios-functions.php:109
msgid "Invalid zip code."
msgstr ""

#: includes/wc-correios-functions.php:110
msgid "Area with delivery temporarily subjected to different periods."
msgstr ""

#: includes/wc-correios-functions.php:111
msgid "The destination CEP is subject to special delivery conditions by ECT and will be carried out with the addition of up to 7 (seven) business days to the regular term."
msgstr ""

#. translators: %s: tracking code
#: includes/wc-correios-functions.php:183
msgid "Added a Correios tracking code: %s"
msgstr ""

#. translators: %s: tracking code
#: includes/wc-correios-functions.php:201
msgid "Removed a Correios tracking code: %s"
msgstr ""

#: templates/emails/correios-tracking-code.php:19
#: templates/emails/plain/correios-tracking-code.php:18
msgid "For your reference, your order details are shown below."
msgstr ""

#. translators: 1: date 2: time
#: templates/myaccount/tracking-history-table.php:15
msgid "%1$s \\a\\t %2$s"
msgstr ""

#: templates/myaccount/tracking-history-table.php:18
msgid "History for the tracking code:"
msgstr ""

#: templates/myaccount/tracking-history-table.php:23
#: templates/myaccount/tracking-history-table.php:31
msgid "Date"
msgstr ""

#: templates/myaccount/tracking-history-table.php:24
#: templates/myaccount/tracking-history-table.php:32
msgid "Location"
msgstr ""

#: templates/myaccount/tracking-history-table.php:25
#: templates/myaccount/tracking-history-table.php:35
msgid "Status"
msgstr ""

#. translators: %s: address
#: templates/myaccount/tracking-history-table.php:42
msgid "In transit to %s"
msgstr ""

#: templates/myaccount/tracking-title.php:15
msgid "Correios delivery tracking"
msgstr ""
