jQuery(document).ready(function($) {
    console.log('Script de depuração carregado');
    
    // Monitora o envio do formulário
    $('#update_price').on('submit', function(e) {
        console.log('Formulário enviado');
        
        // Verifica se há tags selecionadas
        var selectedTags = $('#update_price_field').val();
        console.log('Tags selecionadas:', selectedTags);
        
        // Verifica o valor do campo de preço
        var priceValue = $('#_price_change').val();
        console.log('Valor do preço:', priceValue);
        
        // Se não houver tags selecionadas ou preço, impede o envio
        if (!selectedTags || selectedTags.length === 0 || !priceValue) {
            console.error('Formulário incompleto: tags ou preço não definidos');
            alert('Por favor, selecione pelo menos uma tag e defina um preço.');
            e.preventDefault();
            return false;
        }
    });
});