<?php

function lmpjs_adicionar_menu_admin() {
    add_menu_page(
        'Lmpjs Tag Preço',
        'Tag Preço',
        'manage_woocommerce',
        'lmpjs-tag-preco',
        'lmpjs_render_pagina_configuracao',
        'dashicons-tag',
        56
    );
}
add_action('admin_menu', 'lmpjs_adicionar_menu_admin');


function lmpjs_render_pagina_configuracao() {
    $selected_tags = [];
    $percentual_valor = '';
    $mostrar_simulacao = false;
    $produtos_simulacao = [];

    if (isset($_POST['lmpjs_submit']) || isset($_POST['lmpjs_simulacao'])) {
        $selected_tags = isset($_POST['lmpjs_tags']) ? array_map('intval', $_POST['lmpjs_tags']) : [];
        $percentual_valor = isset($_POST['lmpjs_percentual']) ? sanitize_text_field($_POST['lmpjs_percentual']) : '';
    }

    // Processamento do ajuste real
    if (isset($_POST['lmpjs_submit'])) {
        lmpjs_aplicar_ajuste_preco($selected_tags, floatval(str_replace(',', '.', $percentual_valor)));
        echo '<div class="updated"><p>Ajuste aplicado com sucesso!</p></div>';
    }

    // Preparar dados para simulação (não exibe ainda)
    if (isset($_POST['lmpjs_simulacao'])) {
        $mostrar_simulacao = true;
        $produtos_simulacao = lmpjs_obter_produtos_afetados($selected_tags);
    }

    ?>
    <div class="wrap lmpjs_price">
        <h1>Lmpjs Tag Preço</h1>
        <form method="post">
            <table class="form-table">
                <tr>
                    <th><label for="lmpjs_tags">Tags de Produto:</label></th>
                    <td>
                        <select name="lmpjs_tags[]" id="lmpjs_tags_select" multiple="multiple" style="width: 100%;">
                            <?php
                            if (!empty($selected_tags)) {
                                foreach ($selected_tags as $tag_id) {
                                    $term = get_term($tag_id);
                                    if ($term && !is_wp_error($term)) {
                                        echo '<option value="' . esc_attr($tag_id) . '" selected="selected">' . esc_html($term->name) . '</option>';
                                    }
                                }
                            }
                            ?>
                        </select>
                        <p class="description">Selecione uma ou mais tags de produto.</p>
                    </td>
                </tr>
                <tr>
                    <th><label for="lmpjs_percentual">Ajuste (%):</label></th>
                    <td>
                        <input
                           type="number"
                           name="lmpjs_percentual"
                           value="<?php echo esc_attr($percentual_valor); ?>"
                           required
                           placeholder="Ex: 10 ou -10"
                           step="0.1"
                           min="-1000"
                           max="1000"
                           class="lmpjs-price-input"
                           onkeypress="return event.charCode >= 48 && event.charCode <= 57 || event.charCode == 46 || event.charCode == 45"
                        />
                        <p class="description">Exemplo: 10 para aumentar 10% ou -10 para diminuir 10%</p>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="lmpjs_simulacao" class="button" value="Simular Ajuste">
                <input type="submit" name="lmpjs_submit" class="button button-primary" value="Aplicar Ajuste">
            </p>
        </form>


        <?php
        // Exibe JS Alert antes de prosseguir com a ação de alteração.
         if (!empty($_POST['lmpjs_simulacao']) && !empty($produtos_simulacao)): ?>
            <script>
               document.addEventListener('DOMContentLoaded', function () {
                  const form = document.querySelector('form');
                  const aplicarBtn = form.querySelector('input[name="lmpjs_submit"]');

                  let totalProdutos = <?php echo count($produtos_simulacao); ?>;

                  form.addEventListener('submit', function (e) {
                        const clickedButton = document.activeElement;
                        if (clickedButton && clickedButton.name === 'lmpjs_submit') {
                           const confirmar = confirm(`Você está prestes a aplicar o ajuste em ${totalProdutos} produto(s). Deseja continuar?`);
                           if (!confirmar) {
                              e.preventDefault();
                           }
                        }
                  });
               });
            </script>
         <?php endif; ?>



        <?php
        // Exibir simulação após o formulário
        if ($mostrar_simulacao) {
            lmpjs_exibir_simulacao($produtos_simulacao, floatval(str_replace(',', '.', $percentual_valor)));
        }
        ?>
    </div>
    <?php
}