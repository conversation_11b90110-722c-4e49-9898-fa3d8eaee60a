<?php

/**
 * adiciona informações de META DADOS no header para os produtos
 * Simples e Variáveis
 */

function add_woocommerce_og_product_tags() {
    if ( is_product() ) {
        global $product;

        // Se o objeto $product não existe ou não é um WC_Product, saia
        if ( ! $product || ! is_a( $product, 'WC_Product' ) ) {
            return;
        }

        // --- 1. product:brand ---
        $brand = get_bloginfo('name'); // Padrão: nome da sua loja

        // Obtém os termos da taxonomia 'product_brand' para o produto
        $product_brands = wp_get_post_terms( $product->get_id(), 'product_brand', array( 'fields' => 'names' ) );

        // Se houver marcas atribuídas, use a primeira (ou combine se houver múltiplas)
        if ( ! empty( $product_brands ) && ! is_wp_error( $product_brands ) ) {
            // Geralmente, você quer apenas uma marca para a tag Open Graph.
            // Neste caso a primeira
            $brand = $product_brands[0];
        }

        echo '<meta property="product:brand" content="' . esc_attr($brand) . '">';

        // --- 2. product:condition ---
        // Assumindo que todos os produtos são novos. Altere se tiver produtos usados.
        echo '<meta property="product:condition" content="new">';

        // --- 3. product:price:currency ---
        echo '<meta property="product:price:currency" content="' . esc_attr(get_woocommerce_currency()) . '">';

        // --- Lógica condicional para tags específicas de Simple ou Variable Products ---
        if ( $product->is_type('variable') ) {
            // Para PRODUTOS VARIÁVEIS

            // --- 4. product:price:amount (preço mínimo da variação) ---
            $price_amount = $product->get_variation_price( 'min', false ); // Pega o menor preço entre todas as variações
            echo '<meta property="product:price:amount" content="' . esc_attr($price_amount) . '">';

            // --- 5. product:availability (se qualquer variação estiver em estoque) ---
            $availability = $product->is_in_stock() ? 'in stock' : 'out of stock';
            echo '<meta property="product:availability" content="' . esc_attr($availability) . '">';

            // --- 6. product:retailer_item_id (use o ID do produto pai variável) ---
            echo '<meta property="product:retailer_item_id" content="' . esc_attr($product->get_id()) . '">';

            // --- 7. product:item_group_id (CRUCIAL para produtos variáveis, usa o ID do produto pai) ---
            echo '<meta property="product:item_group_id" content="' . esc_attr($product->get_id()) . '">';

        } elseif ( $product->is_type('simple') ) {
            // Para PRODUTOS SIMPLES

            // --- 4. product:price:amount (preço do produto simples) ---
            $price_amount = $product->get_price();
            echo '<meta property="product:price:amount" content="' . esc_attr($price_amount) . '">';

            // --- 5. product:availability (do produto simples) ---
            $availability = $product->is_in_stock() ? 'in stock' : 'out of stock';
            echo '<meta property="product:availability" content="' . esc_attr($availability) . '">';

            // --- 6. product:retailer_item_id (SKU ou ID do produto simples) ---
            $retailer_item_id = $product->get_sku() ? $product->get_sku() : $product->get_id();
            echo '<meta property="product:retailer_item_id" content="' . esc_attr($retailer_item_id) . '">';

            // --- 7. product:item_group_id (OMITIDO para produtos simples, conforme sua preferência) ---
            // Nenhuma linha de código aqui para esta tag para produtos simples.

        }
    }
}
add_action('wp_head', 'add_woocommerce_og_product_tags');